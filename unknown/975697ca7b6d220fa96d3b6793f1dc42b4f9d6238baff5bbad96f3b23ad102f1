import { sequelize } from "../database/dbConnection.js";
import { Sequelize, Op }from "sequelize";
import Comments from "../models/comments.model.js";
import Tasks from "../models/tasks.model.js";

export const addComment = async (req, res) => {
    try {
        const { task_id, comment_text } = req.body;

        if (!task_id || !comment_text) {
            return res.status(400).json({ error: 'Task ID and Comment text are required.' });
        }

        const task = await Tasks.findOne({ where: { id: task_id } });

        if (!task) {
            return res.status(404).json({ error: 'Task not found.' });
        }

        let dataToSave = {
            task_id,
            user_id: req.user.id,
            comment_text
        };

        const saveComment = await Comments.create(dataToSave);

        return res.status(201).json({
            data: saveComment,
            message: "Comment added successfully"
        });
    } catch (error) {
        console.log('error', error)
        return res.status(500).json({ error: 'Failed to add comment' });
    }
};

  
  
