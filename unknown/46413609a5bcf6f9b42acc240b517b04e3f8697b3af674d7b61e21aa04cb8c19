import express from 'express';
import { authenticateToken } from '../middlewares/jwt.js'
import { createCausalInference, getCausalInferenceById, getCausalOutlierInference } from '../controllers/causalInference.controller.js';
const router = express.Router();

router.post('/', authenticateToken, createCausalInference);
router.get('/:id', authenticateToken, getCausalInferenceById);
router.post('/outlier-inference', authenticateToken, getCausalOutlierInference);
// router.post('/task-list',authenticateToken , getTasksList);
// router.post('/:id/status',authenticateToken , handleStatusChange);

export default router;
