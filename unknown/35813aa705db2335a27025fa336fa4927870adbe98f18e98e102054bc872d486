import express from 'express';
import {authenticateToken} from '../middlewares/jwt.js'
import { createTask, getTasksList, handleStatusChange } from '../controllers/tasks.controller.js';
const router = express.Router();

router.post('/',authenticateToken , createTask);
router.post('/task-list',authenticateToken , getTasksList);
router.post('/:id/status',authenticateToken , handleStatusChange);

export default router;
