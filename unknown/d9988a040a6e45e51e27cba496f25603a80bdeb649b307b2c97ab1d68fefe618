#!/bin/bash

# Process Software Project Docker Setup Script
# This script automates the setup of the Process Software project using Docker

# Color codes for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Function to print colored messages
print_message() {
  echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
  echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
  echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
  print_error "Docker is not installed. Please install Docker first."
  exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
  print_error "Docker Compose is not installed. Please install Docker Compose first."
  exit 1
fi

print_message "Starting Process Software project setup..."

# Check if .env file exists, create if not
if [ ! -f .env ]; then
  print_warning ".env file not found. Creating a default .env file..."
  cat > .env << EOL
NODE_ENV=development
MONGO_INITDB_DATABASE=tvarit

# PostgreSQL credentials
POSTGRES_DB=tvarit
POSTGRES_USER=tvarit_us
POSTGRES_PASSWORD=kjhagfT6sfer

# Database URIs
MONGO_URI=mongodb://mongo:27017/tvarit
DB_HOST=postgres
DB_PORT=5432

# JWT Secret (change this in production)
JWT_SECRET=jksdhfjbdskjbufbsj_gjsf_kjgdskjfhkdsdf5dsf4sd5f64
EOL
  print_message ".env file created successfully."
else
  print_message ".env file already exists. Using existing configuration."
fi

# Check if docker-compose.yml needs port modifications
read -p "Do you need to modify the default ports in docker-compose.yml? (y/n): " modify_ports
if [[ $modify_ports == "y" || $modify_ports == "Y" ]]; then
  print_message "Please modify the docker-compose.yml file manually and then continue."
  read -p "Press Enter to continue after modifying the file..."
fi

# Build and start Docker containers
print_message "Building and starting Docker containers..."
docker-compose up -d

# Check if containers are running
if [ $? -eq 0 ]; then
  print_message "Docker containers started successfully."
else
  print_error "Failed to start Docker containers. Please check the logs."
  exit 1
fi

# Import database if backup file exists
read -p "Do you have a database backup file to import? (y/n): " import_db
if [[ $import_db == "y" || $import_db == "Y" ]]; then
  read -p "Enter the path to the backup file: " backup_file
  
  if [ -f "$backup_file" ]; then
    print_message "Copying backup file to PostgreSQL container..."
    docker cp "$backup_file" process_sw-postgres-1:/tmp/backup.sql
    
    print_message "Importing database backup..."
    docker exec -it process_sw-postgres-1 bash -c "psql -U tvarit_us -d tvarit -f /tmp/backup.sql"
    
    if [ $? -eq 0 ]; then
      print_message "Database imported successfully."
      
      print_message "Restarting backend container to connect to the updated database..."
      docker-compose restart backend
    else
      print_error "Failed to import database. Please check the backup file."
    fi
  else
    print_error "Backup file not found: $backup_file"
  fi
fi

# Display container status
print_message "Container status:"
docker-compose ps

# Display access information
echo ""
print_message "Setup completed successfully!"
print_message "You can access the application at: http://localhost:8081"
print_message "For more information, refer to the DOCKER_SETUP_GUIDE.md file."
