import { DataTypes, Model } from 'sequelize';
import { sequelize } from '../database/dbConnection.js'; // Import the Sequelize instance
import Processes from './processes.js';  // Import the Processes model
import Products from './products.js';  // Import the Products model

class ProcessProduct extends Model {}

ProcessProduct.init(
  {
    process_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: Processes, // Reference to the Processes model
        key: 'id',
      },
      onDelete: 'CASCADE', // Ensures that deleting a process deletes its associated products
    },
    product_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: Products, // Reference to the Products model
        key: 'id',
      },
      onDelete: 'CASCADE', // Ensures that deleting a product deletes its associated processes
    },
  },
  {
    sequelize,
    modelName: 'ProcessProduct',
    tableName: 'process_product',
    timestamps: false,  // This table may not need timestamps (createdAt, updatedAt)
    underscored: true, // Use snake_case for column names in the database
  }
);

// Setting up the associations (Optional, if you want to access related models)
Processes.belongsToMany(Products, {
  through: ProcessProduct,
  foreignKey: 'process_id',
});

Products.belongsToMany(Processes, {
  through: ProcessProduct,
  foreignKey: 'product_id',
});

export default ProcessProduct;
