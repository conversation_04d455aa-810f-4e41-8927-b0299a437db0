import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// === Operation Types ===
interface OperationLine {
  color: string;
  width: number;
  dash: string;
}

export interface OperationRange {
  type: string;
  xref: string;
  yref: string;
  y0: number;
  y1: number;
  line: OperationLine;
  visible?: boolean;
  layer?: string;
  opacity?: number;
  operationId: string;
  selected?:boolean
  operationType?:string
  label?: {
    text: string;
  };
  applyAsFilter?: boolean; // New property to indicate if this operation should be applied as a filter
}

// Grouped by column name
interface OperationGroup {
  columnName: string;
  operations: OperationRange[];
}

// === State Type ===
interface OperationState {
  operations: OperationGroup[];
  selectedOperations: OperationGroup[];
  activeColumnName: string;
}

// === Initial State ===
const initialState: OperationState = {
  operations: [],
  selectedOperations: [],
  activeColumnName: '',
};

// === Slice ===
const operationSlice = createSlice({
  name: 'operations',
  initialState,
  reducers: {
    setOperations: (state, action: PayloadAction<OperationGroup[]>) => {
      state.operations = action.payload;
    },

    addOperation: (
      state,
      action: PayloadAction<{ columnName: string; operation: OperationRange }>
    ) => {
      const { columnName, operation } = action.payload;

      // Look for an existing operations group by columnName
      const group = state.operations.find(g => g.columnName === columnName);

      if (group) {
        group.operations.forEach(op => op.selected = false); // This is to make all operational ranges selected false and newly created true
        group.operations.push({ ...operation, selected: true ,operationType:'operation'}); // Append to existing array
      } else {
        state.operations.push({
          columnName,
          operations: [{ ...operation, selected: true ,operationType:'operation'}] // Create new group with one operation
        });
      }

      // Also add to selected operations
      const exists = state.selectedOperations.some(
        (group) => group.columnName === columnName && group.operations.some(o => o.operationId === operation.operationId)
      );

      if (!exists) {
        const selectedGroup = state.selectedOperations.find(g => g.columnName === columnName);
        if (selectedGroup) {
          selectedGroup.operations.push(operation);
        } else {
          state.selectedOperations.push({ columnName, operations: [operation] });
        }
      }
    },

    updateOperation: (
      state,
      action: PayloadAction<{
        columnName: string;
        operationId: string;
        newLabelText: string;
      }>
    ) => {
      const { columnName, operationId, newLabelText } = action.payload;
      const group = state.operations.find(g => g.columnName === columnName);

      if (group) {
        const operation = group.operations.find(o => o.operationId === operationId);
        if (operation) {
          if (!operation.label) {
            operation.label = { text: '' };
          }
          operation.label.text = newLabelText || '';
        }
      }

      const selectedGroup = state.selectedOperations.find(g => g.columnName === columnName);
      if (selectedGroup) {
        const selectedOperation = selectedGroup.operations.find(o => o.operationId === operationId);
        if (selectedOperation) {
          if (!selectedOperation.label) {
            selectedOperation.label = { text: '' };
          }
          selectedOperation.label.text = newLabelText || '';
        }
      }
    },

    removeOperation: (
      state,
      action: PayloadAction<{ columnName: string; operationId: string }>
    ) => {
      const { columnName, operationId } = action.payload;
      const group = state.operations.find(g => g.columnName === columnName);

      if (group) {
        group.operations = group.operations.filter(o => o.operationId !== operationId);
        if (group.operations.length === 0) {
          state.operations = state.operations.filter(g => g.columnName !== columnName);
        }
      }

      const selectedGroup = state.selectedOperations.find(g => g.columnName === columnName);
      if (selectedGroup) {
        selectedGroup.operations = selectedGroup.operations.filter(o => o.operationId !== operationId);
        if (selectedGroup.operations.length === 0) {
          state.selectedOperations = state.selectedOperations.filter(g => g.columnName !== columnName);
        }
      }
    },

    clearOperations: (state) => {
      state.operations = [];
      state.selectedOperations = [];
    },

    toggleSelectedOperation: (
      state,
      action: PayloadAction<{ columnName: string; operation: OperationRange }>
    ) => {
      const { columnName, operation } = action.payload;
    
      // Step 1: Toggle `selected` in main `operations` state
      const opGroup = state.operations.find(g => g.columnName === columnName);
      if (opGroup) {
        opGroup.operations.forEach(o => {
          o.selected = o.operationId === operation.operationId ? !o.selected : false;
        });
      }
    
      // Step 2: Update selectedOperations group
      const selectedGroup = state.selectedOperations.find(g => g.columnName === columnName);
    
      if (selectedGroup) {
        const index = selectedGroup.operations.findIndex(o => o.operationId === operation.operationId);
    
        if (index !== -1) {
          selectedGroup.operations.splice(index, 1); // remove from selected
          if (selectedGroup.operations.length === 0) {
            state.selectedOperations = state.selectedOperations.filter(g => g.columnName !== columnName);
          }
        } else {
          selectedGroup.operations.push({ ...operation, selected: !operation.selected }); // add with toggled selected
        }
      } else {
        state.selectedOperations.push({
          columnName,
          operations: [{ ...operation, selected: !operation.selected }],
        });
      }
    }
    ,

    setActiveColumnName: (state, action: PayloadAction<string>) => {
      state.activeColumnName = action.payload;
    },

    setSelectedOperations: (state, action: PayloadAction<OperationGroup[]>) => {
      state.selectedOperations = action.payload;
    },

    toggleOperationFilter: (
      state,
      action: PayloadAction<{ columnName: string; operationId: string }>
    ) => {
      const { columnName, operationId } = action.payload;
      const group = state.operations.find(g => g.columnName === columnName);
      if (group) {
        const operation = group.operations.find(o => o.operationId === operationId);
        if (operation) {
          // Toggle the applyAsFilter property
          operation.applyAsFilter = !operation.applyAsFilter;
        }
      }
    },
  },
});

export const {
  setOperations,
  addOperation,
  updateOperation,
  removeOperation,
  clearOperations,
  toggleSelectedOperation,
  setSelectedOperations,
  setActiveColumnName,
  toggleOperationFilter,
} = operationSlice.actions;

export default operationSlice.reducer;
