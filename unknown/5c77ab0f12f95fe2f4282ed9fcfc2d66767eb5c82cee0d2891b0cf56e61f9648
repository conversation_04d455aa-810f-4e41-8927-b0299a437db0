import { sequelize } from "../database/dbConnection.js";
import { Sequelize, Op }from "sequelize";
import WorkflowShared from '../models/workflowShared.model.js';
import User from "../models/user.model.js";


export const createSharedWorkflow = async (req, res) => {
  try {
    const { share_to, shared_to_id, access_level, share_type, share_type_id } = req.body;

    let sharedWorkflows = [];

    if (share_to === 'users' && Array.isArray(shared_to_id)) {
        sharedWorkflows = await Promise.all(
            shared_to_id.map(async user =>{
             const response = await  WorkflowShared.create({
                user_id: req.user.id,
                share_to,
                shared_to_id: user.id,
                access_level: user.accessType, 
                share_type,
                share_type_id,
            })
            return response
          })
        );
    } else {
        const sharedWorkflow = await WorkflowShared.create({
            user_id: req.user.id,
            share_to,
            shared_to_id: req.user.tenant_id,
            access_level,
            share_type,
            share_type_id,
        });
        sharedWorkflows.push(sharedWorkflow);
    }

      // res.status(201).json(sharedWorkflows);
      return res.status(200).json({
        message: 'Shared Workflow fetched successfully',
        data: sharedWorkflows,
    });
  } catch (error) {
      console.error('Error sharing workflow:', error);
      res.status(500).json({ error: 'Failed to share workflow' });
  }
};

export const getSharedWorkflow = async (req, res) => {
  try {
    const shared_to_id = req.user.id;

    const user = await User.findOne({ where: { id: shared_to_id }, attributes: ['tenant_id'] });
    if (!user) {
      return res.status(404).json({ error: 'User not found' });
    }

    const tenant_id = user.tenant_id;
    const systemsIdParam = req.query?.systems_id;

    let whereCondition = `((ws.share_to = 'tenant' AND ws.shared_to_id = ${tenant_id} AND ws.user_id != ${shared_to_id})
    OR (ws.share_to = 'users' AND ws.shared_to_id = ${shared_to_id})) `


    let arrayLiteral
    if (systemsIdParam && typeof systemsIdParam === 'string') {
      const systemsNameArray = systemsIdParam.split(',').map((name) => name.trim());
      
      if (systemsNameArray.length > 0) {
        const sortedSystemsNameArray = systemsNameArray.sort();
        arrayLiteral = `{${sortedSystemsNameArray.join(',')}}`;
      }
      if(arrayLiteral){
        whereCondition += `AND W."systems_id" @> '${arrayLiteral}' AND w."systems_id" <@ '${arrayLiteral}'`
      }
    }


    // Fetch latest shared workflows per workflow ID
    const sharedWorkflows = await sequelize.query(
      `WITH RankedWorkflows AS (
          SELECT DISTINCT ON (ws.share_type_id) 
              ws.id, 
              ws.user_id, 
              ws.share_to, 
              ws.shared_to_id, 
              ws.access_level, 
              ws.share_type, 
              ws.share_type_id, 
              ws.created_at, 
              ws.updated_at, 
              w.id AS workflow_id,
              w.name AS workflow_name
          FROM workflow_shared ws
          LEFT JOIN workflows w 
            ON (ws.share_type = 'workflow' AND ws.share_type_id::integer = w.id)
          WHERE ${whereCondition}
          ORDER BY ws.share_type_id, ws.created_at DESC
          )
          SELECT * FROM RankedWorkflows;`,
          {
            type: Sequelize.QueryTypes.SELECT,
            replacements: { tenant_id, shared_to_id }
          }
        );
        // JOIN workflows w ON ws.share_type_id::integer = w.id
    // JOIN workflows w ON ws.share_type_id = w.id
    

    // Format the response
    const response = {
      id: 0,
      name: "Shared Workflow",
      parent_id: null,
      user_id: 1,
      systems_id: null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
      type: "folder",
      children: sharedWorkflows.map(workflow => ({
        id: workflow.workflow_id,
        name: workflow.workflow_name,
        access_level: workflow.access_level,
        share_type: workflow.share_type,
        type: "file",
        children: [],
      })),
    };

    return res.status(200).json({
      message: 'Shared workflows fetched successfully',
      data: response,
    });
  } catch (error) {
    console.error('Error fetching shared workflows:', error);
    return res.status(500).json({ error: 'Failed to fetch shared workflows' });
  }
};

