import { DataTypes, Model } from 'sequelize';
import { sequelize } from '../database/dbConnection.js'; // Import the Sequelize instance
import Products from './products.model.js';  // Import the Products model
import Materials from './materials.model.js';  // Import the Materials model

class ProductMaterials extends Model {}

ProductMaterials.init(
  {
    product_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: Products, // Reference to the Products model
        key: 'id',
      },
      onDelete: 'CASCADE', // Ensures that deleting a product deletes its associated materials
    },
    material_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: Materials, // Reference to the Materials model
        key: 'id',
      },
      onDelete: 'CASCADE', // Ensures that deleting a material deletes its associated products
    },
  },
  {
    sequelize,
    modelName: 'ProductMaterials',
    tableName: 'product_materials',
    timestamps: false,  // This table may not need timestamps (createdAt, updatedAt)
    underscored: true, // Use snake_case for column names in the database
  }
);

// Setting up the associations (Optional, if you want to access related models)
Products.belongsToMany(Materials, {
  through: ProductMaterials,
  foreignKey: 'product_id',
});

Materials.belongsToMany(Products, {
  through: ProductMaterials,
  foreignKey: 'material_id',
});

export default ProductMaterials;
