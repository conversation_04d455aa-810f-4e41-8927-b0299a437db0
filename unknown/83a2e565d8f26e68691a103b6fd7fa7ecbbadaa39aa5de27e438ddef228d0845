import WorkflowShared from '../models/workflowShared.model.js';

export const createSharedResult = async (req, res) => {
  try {
    const { share_to, shared_to_id, run_ids, workflow_id } = req.body;
    let sharedEntries = [];

    // Handle user-based sharing
    if (share_to === 'users' && Array.isArray(shared_to_id)) {
      // For each user
      for (const user of shared_to_id) {
        // 1. Create workflow-level entry with access_level as 'runs'
        const workflowEntry = await WorkflowShared.create({
          user_id: req.user.id,
          share_to: 'users',
          shared_to_id: user.id,
          access_level: 'runs',
          share_type: 'workflow',
          share_type_id: workflow_id
        });
        sharedEntries.push(workflowEntry);

        // 2. Create run-level entries for each run
        for (const run_id of run_ids) {
          const runEntry = await WorkflowShared.create({
            user_id: req.user.id,
            share_to: 'users',
            shared_to_id: user.id,
            access_level: 'view',
            share_type: 'runs',
            share_type_id: run_id
          });
          sharedEntries.push(runEntry);
        }
      }
    }
    // Handle tenant-based sharing
    else if (share_to === 'tenant') {
      // 1. Create workflow-level entry for tenant
      const workflowEntry = await WorkflowShared.create({
        user_id: req.user.id,
        share_to: 'tenant',
        shared_to_id: req.user.tenant_id,
        access_level: 'runs',
        share_type: 'workflow',
        share_type_id: workflow_id
      });
      sharedEntries.push(workflowEntry);

      // 2. Create run-level entries for each run
      for (const run_id of run_ids) {
        const runEntry = await WorkflowShared.create({
          user_id: req.user.id,
          share_to: 'tenant',
          shared_to_id: req.user.tenant_id,
          access_level: 'view',
          share_type: 'runs',
          share_type_id: run_id
        });
        sharedEntries.push(runEntry);
      }
    }

    return res.status(200).json({
      message: 'Workflow runs shared successfully',
      data: sharedEntries,
    });
  } catch (error) {
    console.error('Error sharing workflow runs:', error);
    res.status(500).json({ error: 'Failed to share workflow runs' });
  }
}; 