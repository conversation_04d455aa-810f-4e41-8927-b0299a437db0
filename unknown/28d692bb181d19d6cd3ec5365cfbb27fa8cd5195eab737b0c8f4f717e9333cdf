# Docker Commands Quick Reference

This document provides a quick reference for common Docker commands used with the Process Software project.

## Container Management

### Start all containers
```bash
docker-compose up -d
```

### Stop all containers
```bash
docker-compose down
```

### Restart a specific container
```bash
docker-compose restart backend
docker-compose restart postgres
docker-compose restart mongo
docker-compose restart redis
```

### View container logs
```bash
# View logs for a specific container
docker-compose logs backend
docker-compose logs postgres
docker-compose logs mongo

# Follow logs in real-time
docker-compose logs -f backend

# View last 100 lines of logs
docker-compose logs --tail=100 backend
```

### Check container status
```bash
docker-compose ps
```

## Database Operations

### Access PostgreSQL CLI
```bash
docker exec -it process_sw-postgres-1 psql -U tvarit_us -d tvarit
```

### Execute PostgreSQL query
```bash
docker exec -it process_sw-postgres-1 psql -U tvarit_us -d tvarit -c "SELECT * FROM users LIMIT 5;"
```

### List PostgreSQL tables
```bash
docker exec -it process_sw-postgres-1 psql -U tvarit_us -d tvarit -c "\dt"
```

### Import database from backup
```bash
# Copy backup file to container
docker cp backup.sql process_sw-postgres-1:/tmp/

# Import backup
docker exec -it process_sw-postgres-1 bash -c "psql -U tvarit_us -d tvarit -f /tmp/backup.sql"
```

### Export database to backup
```bash
docker exec -it process_sw-postgres-1 bash -c "pg_dump -U tvarit_us tvarit > /tmp/backup.sql"
docker cp process_sw-postgres-1:/tmp/backup.sql ./backup.sql
```

### Access MongoDB CLI
```bash
docker exec -it process_sw-mongo-1 mongosh
```

### Execute MongoDB query
```bash
docker exec -it process_sw-mongo-1 mongosh --eval "db.getSiblingDB('tvarit').getCollectionNames()"
```

## Container Shell Access

### Access backend container shell
```bash
docker exec -it process_sw-backend-1 bash
```

### Access PostgreSQL container shell
```bash
docker exec -it process_sw-postgres-1 bash
```

### Access MongoDB container shell
```bash
docker exec -it process_sw-mongo-1 bash
```

### Access Redis container shell
```bash
docker exec -it process_sw-redis-1 sh
```

## Volume Management

### List volumes
```bash
docker volume ls | grep process_sw
```

### Inspect volume
```bash
docker volume inspect process_sw_postgres_data
docker volume inspect process_sw_mongo_data
```

### Remove volumes (WARNING: This will delete all data)
```bash
docker-compose down -v
```

## Network Management

### List networks
```bash
docker network ls | grep process_sw
```

### Inspect network
```bash
docker network inspect process_sw_default
```

## Troubleshooting

### Check container resource usage
```bash
docker stats
```

### Check container configuration
```bash
docker inspect process_sw-backend-1
```

### Rebuild containers
```bash
docker-compose up -d --build
```

### Completely reset environment
```bash
# Stop containers and remove volumes
docker-compose down -v

# Remove all unused containers, networks, and images
docker system prune -a

# Start fresh
docker-compose up -d
```

## Development Workflow

### View changes in real-time
The backend code is mounted as a volume, so changes to the code will be reflected in the container. However, you may need to restart the backend container to apply changes:

```bash
docker-compose restart backend
```

### Install new npm packages
```bash
docker exec -it process_sw-backend-1 npm install <package-name>
```

### Run npm scripts
```bash
docker exec -it process_sw-backend-1 npm run <script-name>
```
