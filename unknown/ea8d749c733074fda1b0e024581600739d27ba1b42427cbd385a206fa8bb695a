// const { DataTypes } = require('sequelize');
import { DataTypes } from "sequelize";
import { sequelize } from "../database/dbConnection.js";
import workflowFilters from "./workflowFilters.model.js";
const GoldenValueData = sequelize.define('golden_value_data', {
    id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
    },
    user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
            model: 'Users',
            key: 'id'
        }
    },
    name: {
        type: DataTypes.STRING,
        allowNull: false,
    },
    workflow_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
            model: 'workflows',
            key: 'id'
        },
        onDelete: 'CASCADE'
    },
    hide_for_s3: {
        type: DataTypes.BOOLEAN,
        defaultValue: false, 
        allowNull: false, 
    },
    filter_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
    },
    golden_value: {
        type: DataTypes.JSON,
        allowNull: false,
    },
    golden_run_data: {
        type: DataTypes.JSON,
        allowNull: true,
    },
    settings: {
        type: DataTypes.JSON,
        allowNull: true,
    },
    custom_parameter: {
        type: DataTypes.BOOLEAN,
        defaultValue: false, 
    },
    cluster_data:{
        type: DataTypes.JSON,
        allowNull: true,
    },
    mljob_run_id: {
        type: DataTypes.STRING,
    },
    created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
    },
    updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
    },
}, {
    tableName: 'golden_value_data',
    timestamps: false,
});

GoldenValueData.belongsTo(workflowFilters, { 
    foreignKey: 'filter_id',
    as: 'workflowFilter'  // Alias name for the association
  });

export default GoldenValueData;
