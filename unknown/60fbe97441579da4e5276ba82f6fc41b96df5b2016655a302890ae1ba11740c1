import { DataTypes } from "sequelize";
import { sequelize } from "../database/dbConnection.js";
import Workflow from "./workflow.model.js";
import WorkflowComponents from "./workflowComponents.model.js";

const WorkflowStructure = sequelize.define('workflowstructures', {
    id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
    },
    workflow_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
            model: 'workflows',
            key: 'id'
        },
        onDelete: 'CASCADE'
    },
    hierarchy: {
        type: DataTypes.JSON,
        allowNull: false,
    },
    created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
    },
    updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
    },
}, {
    tableName: 'workflowstructures',
    timestamps: false,
});

// Workflow.hasMany(WorkflowStructure, { foreignKey: 'workflow_id', onDelete: 'CASCADE' });
// Workflow.hasMany(WorkflowComponents, { foreignKey: 'workflow_id', onDelete: 'CASCADE' });

// WorkflowStructure.belongsTo(Workflow, { foreignKey: 'workflow_id' });
// WorkflowComponents.belongsTo(Workflow, { foreignKey: 'workflow_id' });

WorkflowStructure.associate = () => {
    const { Workflow, WorkflowStructure } = sequelize.models;
    
    // Workflow.hasMany(WorkflowStructure, { foreignKey: 'workflow_id', onDelete: 'CASCADE' });
    // Workflow.hasMany(WorkflowComponents, { foreignKey: 'workflow_id', onDelete: 'CASCADE' });
    
    // WorkflowStructure.belongsTo(Workflow, { foreignKey: 'workflow_id' });
    // WorkflowComponents.belongsTo(Workflow, { foreignKey: 'workflow_id' });

    WorkflowStructure.belongsTo(Workflow, {
        foreignKey: 'workflow_id',
        as: 'workflow',
    });
};

export default WorkflowStructure;
