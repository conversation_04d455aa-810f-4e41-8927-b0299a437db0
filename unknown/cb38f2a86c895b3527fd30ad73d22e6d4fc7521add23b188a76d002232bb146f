import Tasks from '../models/tasks.model.js';
import { sequelize } from "../database/dbConnection.js";
import { Sequelize, Op }from "sequelize";
import Comments from '../models/comments.model.js';

export const createTask = async (req, res) => {
    try {
        // Add tenant_id from the logged-in user
        const taskData = {
            ...req.body,
            user_id: req.user.id,
            tenant_id: req.user.tenant_id
        };

        const newTask = await Tasks.create(taskData);

        let commentData = {
            user_id: req.user.id,
            comment_text: req.body.comment,
            task_id: newTask.dataValues.id
        };
        
        const saveComments = await Comments.create(commentData);

        return res.status(201).json({
            message: "Task Created successfully.",
            data: newTask
        });
    } catch (error) {
        console.error('errorrr', error);
        return res.status(500).json({ error: 'Failed to create tasks' });
    }
};

export const getTasksList = async (req, res) => {
    try {
        const { filters, dateRange, status, userToFind } = req.body;
        
        // Check if user is a process engineer
        const isProcessEngineer = req.user.role.name === 'Process Engineer';
        
        const buildFilterConditions = (filterGroup) => {
            if (!filterGroup || !filterGroup.rules || filterGroup.rules.length === 0) {
                return '';
            }

            const conditions = filterGroup.rules.filter((rule)=>rule.value).map((rule) => {
                switch (rule.operator) {
                    case '=':
                        if (rule.field === 'recipeNo') {
                            return `t.generic_identifier->>'recipeNo' = '${rule.value}'`;
                        }
                        if (rule.field === 'vatNo') {
                            return `t.generic_identifier->>'vatNo' = '${rule.value}'`;
                        }
                        if (rule.field === 'sequenceNo') {
                            return `t.generic_identifier->>'sequenceNo' = '${rule.value}'`;
                        }
                        return `${rule.field} = '${rule.value}'`;
                    case 'contains':
                        if (rule.field === 'recipeNo') {
                            return `t.generic_identifier->>'recipeNo' ILIKE '%${rule.value}%'`;
                        }
                        if (rule.field === 'vatNo') {
                            return `t.generic_identifier->>'vatNo' ILIKE '%${rule.value}%'`;
                        }
                        if (rule.field === 'sequenceNo') {
                            return `t.generic_identifier->>'sequenceNo' ILIKE '%${rule.value}%'`;
                        }
                        return `${rule.field} ILIKE '%${rule.value}%'`;
                    case '>':
                        return `${rule.field} > '${rule.value}'`;
                    case '<':
                        return `${rule.field} < '${rule.value}'`;
                    case '>=':
                        return `${rule.field} >= '${rule.value}'`;
                    case '<=':
                        return `${rule.field} <= '${rule.value}'`;
                    default:
                        return '';
                }
            });

            return conditions.length > 0 ? `AND (${conditions.join(` ${filterGroup.combinator.toUpperCase()} `)})` : '';
        };

        // Build filter conditions for tasks
        let filterConditions = buildFilterConditions(filters);
        console.log('filterConditions', filterConditions)

        if (dateRange && dateRange?.startDate && dateRange?.endDate) {
            const startDate = dateRange?.startDate;
            const endDate = dateRange?.endDate;
            filterConditions += ` AND t.created_at BETWEEN '${startDate}' AND '${endDate}'`;
        }

        // if (status) {
        //     filterConditions += ` AND t.status = '${status}'`;
        // }
        if (status && Array.isArray(status) && status.length > 0) {
            const statusConditions = status.map((s) => `'${s}'`).join(', ');
            filterConditions += ` AND t.status IN (${statusConditions})`;
        }

        // const todoFilter = userToFind ? ` AND t.user_id = '${userToFind}'` : '';
        // const assignedFilter = userToFind ? ` AND t.assign_to = '${userToFind}'` : '';

        // Modify user filters based on role
        // const userFilter = isProcessEngineer
        //     ? '' // No user filter for process engineers - they see all tasks
        //     : userToFind && Array.isArray(userToFind) && userToFind.length > 0
        //         ? ` AND t.user_id IN (${userToFind.map((id) => `'${id}'`).join(', ')})`
        //         : '';


        const userFilter =  userToFind && Array.isArray(userToFind) && userToFind.length > 0
                ? ` AND t.user_id IN (${userToFind.map((id) => `'${id}'`).join(', ')})`
                : '';

        // const assigneeFilter = isProcessEngineer
        //     ? '' // No assignee filter for process engineers - they see all tasks
        //     : userToFind && Array.isArray(userToFind) && userToFind.length > 0
        //         ? ` AND t.assign_to IN (${userToFind.map((id) => `'${id}'`).join(', ')})`
        //         : '';
        const assigneeFilter = userToFind && Array.isArray(userToFind) && userToFind.length > 0
        ? ` AND t.assign_to IN (${userToFind.map((id) => `'${id}'`).join(', ')})`
        : '';
        
        // Base query for all tasks
        const baseQuery = `
                SELECT t.*,
                    JSON_BUILD_OBJECT(
                        'user_id', u.id,
                        'first_name', u.first_name,
                        'last_name', u.last_name,
                        'email', u.email,
                        'role', JSON_BUILD_OBJECT(
                            'name', r.name,
                            'alias', r.alias,
                            'rank', r.rank
                        )
                    ) AS user_id,
                    JSON_BUILD_OBJECT(
                        'user_id', u2.id,
                        'first_name', u2.first_name,
                        'last_name', u2.last_name,
                        'email', u2.email,
                        'role', JSON_BUILD_OBJECT(
                            'name', r2.name,
                            'alias', r2.alias,
                            'rank', r2.rank
                        )
                    ) AS assign_to,
                    COALESCE(
                        (SELECT JSON_AGG(
                                JSON_BUILD_OBJECT(
                                    'comment_id', c.id,
                                    'comment_text', c.comment_text,
                                    'created_at', c.created_at,
                                    'user', JSON_BUILD_OBJECT(
                                        'user_id', cu.id,
                                        'first_name', cu.first_name,
                                        'last_name', cu.last_name,
                                        'email', cu.email
                                    )
                                )
                            )
                            FROM comments c
                            LEFT JOIN Users cu ON c.user_id = cu.id
                            WHERE c.task_id = t.id),
                        '[]'
                    ) AS comments
                FROM Tasks t
                LEFT JOIN Users u ON t.user_id = u.id
                LEFT JOIN Users u2 ON t.assign_to = u2.id
                LEFT JOIN Roles r ON u.role_id = r.id
                LEFT JOIN Roles r2 ON u2.role_id = r2.id
                WHERE t.tenant_id = :tenantId
                ${filterConditions}
            `;

            if (isProcessEngineer) {
                // For process engineers, get all tasks within their tenant
                let allTaskFilter = `${baseQuery} ${userFilter}`
                let assignedTaskFilter = `${baseQuery} ${assigneeFilter}`
                const [allTasks, assignedTasks] = await Promise.all([
                    // Query for all tasks
                    sequelize.query(
                        `${allTaskFilter} ORDER BY t.created_at ASC`,
                        {
                            replacements: {
                                tenantId: req.user.tenant_id
                            },
                            type: Sequelize.QueryTypes.SELECT
                        }
                    ),
                    // Query for tasks assigned by the process engineer to others
                    sequelize.query(
                        `${assignedTaskFilter} ORDER BY t.created_at ASC`,
                        {
                            replacements: {
                                userId: req.user.id,
                                tenantId: req.user.tenant_id
                            },
                            type: Sequelize.QueryTypes.SELECT
                        }
                    )
                ]);
                // console.log("Assigned", assignedTasks);
                
    
                return res.status(200).json({
                    message: "Tasks fetched successfully.",
                    todoTasks: allTasks,
                    assignedTasks: assignedTasks,
                    isProcessEngineer: true
                });
        } else {
            // For other users, get their todo and assigned tasks within their tenant
            const [todoTasks, assignedTasks] = await Promise.all([
                sequelize.query(
                    `${baseQuery} AND t.assign_to = :userId ${userFilter} ORDER BY t.created_at ASC`,
                    {
                        replacements: {
                            userId: req.user.id,
                            tenantId: req.user.tenant_id
                        },
                        type: Sequelize.QueryTypes.SELECT
                    }
                ),
                sequelize.query(
                    `${baseQuery} AND t.user_id = :userId AND t.assign_to != :userId ${assigneeFilter} ORDER BY t.created_at ASC`,
                    {
                      replacements: {
                            userId: req.user.id,
                            tenantId: req.user.tenant_id
                        },
                        type: Sequelize.QueryTypes.SELECT
                    }
                )
            ]);

            return res.status(200).json({
                message: "Tasks fetched successfully.",
                todoTasks,
                assignedTasks,
                isProcessEngineer: false
            });
        }

    } catch (error) {
        console.error('error', error);
        return res.status(500).json({ error: 'Failed to fetch tasks' });
    }
};

export const handleStatusChange = async (req, res) => {
    try {
        const { id } = req.params;
        const { status, completed_comment } = req.body;
    
       const validStatuses = [
         "todo",
         "in_progress",
         "completed",
         "not_verified",
       ];
        if (!validStatuses.includes(status)) {
          return res.status(400).json({ message: 'Invalid status value.' });
        }
    
        const task = await Tasks.findByPk(id);
        if (!task) {
          return res.status(404).json({ message: 'Task not found.' });
        }
    
        task.status = status;
        if(completed_comment)
            task.completed_comment = completed_comment;
        await task.save();
    
        res.status(200).json({ message: 'Task status updated successfully.', task });
      } catch (error) {
        console.error('Error updating task status:', error);
        res.status(500).json({ message: 'Internal server error.' });
      }
};