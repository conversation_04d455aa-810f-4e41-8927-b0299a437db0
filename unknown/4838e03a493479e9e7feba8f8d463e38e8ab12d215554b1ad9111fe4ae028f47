import { sequelize } from "../database/dbConnection.js";
import { generateResponse } from "../utils/commonResponse.js";
import CSVFilesSchema from "../models/file.model.js"
import User from "../models/user.model.js";

import workflowFilters from "../models/workflowFilters.model.js";
import Workflow from "../models/workflow.model.js";


const workflowFilterList = async (req , res) =>{
    try {
        const {id} = req.user

          const filterList = await workflowFilters.findAll({
            attributes: ['id', 'name', 'version' , 'csv_id', 'systems_id', 'is_default_setting', 'tenant_id', 'filter_type'],
            where: { user_id: id },
            order: [['created_at', 'DESC']],
          });

          if (!filterList.length) {
            return generateResponse(res, 200, 'No filters are available',[]);
          }
          return generateResponse(res, 200, 'File list is fetched successfully', filterList);
        
    } catch (error) {
        console.log('error :', error);
        return generateResponse(res, 500, 'Failed to fetch files');
    }


}

const workflowFilterDetails = async (req , res) =>{
    try {
        const {id} = req.params

          const filterList = await workflowFilters.findAll({
            attributes: ['id', 'name', 'filters' , 'version' , 'csv_id', 'systems_id', 'is_default_setting'],
            where: { id: id }
          });

          if (!filterList.length) {
            return generateResponse(res, 200, 'No filters are available',[]);
          }
          return generateResponse(res, 200, 'Filter Details are fetched successfully', filterList);
        
    } catch (error) {
        console.log('error :', error);
        return generateResponse(res, 500, 'Failed to fetch files');
    }
}

const createFilter = async (req, res) => {
  try {
    let { workflowComponents, workflowStructure, workflowId, filter } = req.body;
    const { tenant_id } = req.user;

    if (!filter || !workflowId) {
      return generateResponse(res, 400, 'Invalid request data');
    }

    console.log('filters-------------', filter);

    let dataToSave = {};

    if (filter.type === 'no-change') {
        dataToSave.filter_id = filter.id;
    }

    if (filter.custom_type === 'no-change') {
        dataToSave.custom_filter_id = filter.custom_id;
    }


    if (Object.keys(dataToSave).length > 0) {
      await Workflow.update(dataToSave, { where: { id: workflowId } });
    }

    let workflowSettingRules = {};
    let workflowSettingGlobalTarget = {};

    workflowComponents?.forEach((res) => {
      let setting = { type: res.type, setting: res.settings };

      if (res.type === 'file') {
        workflowSettingRules[res.type] = (({ global_query, target_variable_settings, ...rest }) => rest)(setting.setting);
        workflowSettingGlobalTarget[res.type] = {
          global: setting.setting.global_query,
          target_variable_settings: setting.setting.target_variable_settings,
          exclude_features: setting.setting?.exclude_features || []
        };
      }
    });

    const fileNode = workflowStructure?.hierarchy?.nodes.find(node => node?.data?.type === 'file');
    const csvId = fileNode ? fileNode?.data?.id : null;
    const systems = Array.isArray(filter?.selectedSystems) ? filter.selectedSystems : JSON.parse(filter?.selectedSystems || '[]');
    const systemIds = systems.map(item => item.systemId);

    let filtersRules, filtersGlobalTarget;

    if (filter.custom_type === 'create') {
      console.log("logogogogogog");
      
      filtersRules = await workflowFilters.create({
        name: filter.name,
        workflow_id: workflowId,
        user_id: req.user.id,
        csv_id: csvId,
        filters: workflowSettingRules,
        version: 1,
        filter_type: 'custom',
        tenant_id: tenant_id,
        is_default_setting: filter.isDefaultSetting,
        systems_id: systemIds,
      });

      await Workflow.update({ custom_filter_id: filtersRules.id }, { where: { id: workflowId } });
    }
    
    if (filter.type === 'create') {
      console.log("logogogogogog111111");

      filtersGlobalTarget = await workflowFilters.create({
        name: filter.name2,
        workflow_id: workflowId,
        user_id: req.user.id,
        csv_id: csvId,
        filters: workflowSettingGlobalTarget,
        version: 1,
        filter_type: 'global',
        tenant_id: tenant_id,
        is_default_setting: filter.isDefaultSetting,
        systems_id: systemIds,
      });

      await Workflow.update({ filter_id: filtersGlobalTarget.id }, { where: { id: workflowId } });
    }

    if (filter.type === 'update') {

      await Workflow.update({ filter_id: filter.id }, { where: { id: workflowId } });

      const [updatedFilterGlobalTarget] = await workflowFilters.update(
        { filters: workflowSettingGlobalTarget },
        { where: { id: filter.id } }
      );
    }
    if (filter.custom_type === 'update') {

      await Workflow.update({ custom_filter_id: filter.custom_id }, { where: { id: workflowId } });

      const [updatedFilterRules] = await workflowFilters.update(
        { filters: workflowSettingRules },
        { where: { id: filter.custom_id } }
      );
    }

    return generateResponse(res, 200, 'Filter operation completed successfully', { filtersRules, filtersGlobalTarget });
  } catch (error) {
    console.error('Error creating filter:', error);
    return generateResponse(res, 500, 'Failed to create filter');
  }
};



const updateFilterName = async (req, res ) =>{
  try {
    const {id} = req.params
    const lastWorkflowRecord = await workflowFilters.findOne({
      where: { id: id },
      order: [['created_at', 'DESC']],
      attributes: ['id', 'name', 'workflow_id', 'created_at', 'version'],
    });

    // Check if the record exists
    if (!lastWorkflowRecord) {
      return generateResponse(res, 500, 'No record found for the given Filter ID.');
    }
    const updatedFilter = await workflowFilters.update(
      { name: req.body.updatedName }, // Fields to update
      { where: { id: lastWorkflowRecord.id } } // Condition to identify the record
    );
    return generateResponse(res, 200, 'Filter Details are fetched successfully', updatedFilter);
  } catch (error) {
  console.log('error :', error);
  return generateResponse(res, 500, 'Error while updating the filter name');
  }
}



const workflowStoredFilter = async (req, res) =>{
  try {
    const {id} = req.params
    const workflowFilter = await Workflow.findOne(
      {
        where: { id: id }
      }
      )
    if (!workflowFilter || !workflowFilter?.filter_id) {
      return generateResponse(res, 200, 'Previously there was no filter saved',[]);
    }
    if(workflowFilter?.filter_id){
      let filterList = await workflowFilters.findAll({
        attributes: ['id', 'name', 'filters' , 'version' , 'csv_id', 'systems_id', 'is_default_setting'],
        where: { id: workflowFilter.filter_id }
      });

      let customFilterList
      if(workflowFilter?.custom_filter_id){
        customFilterList = await workflowFilters.findAll({
          attributes: ['id', 'name', 'filters' , 'version' , 'csv_id', 'systems_id', 'is_default_setting'],
          where: { id: workflowFilter.custom_filter_id }
        });
        
      }
      console.log('customFilterList', customFilterList)
      if(customFilterList?.length)
        filterList.push(...customFilterList)

      if (!filterList.length) {
        return generateResponse(res, 200, 'No filters are available',[]);
      }
      return generateResponse(res, 200, 'Filter Details are fetched successfully', filterList);
    }
  } catch (error) {
    
  }
}








export {
    workflowFilterList,
    workflowFilterDetails,
    createFilter,
    updateFilterName,
    workflowStoredFilter
}