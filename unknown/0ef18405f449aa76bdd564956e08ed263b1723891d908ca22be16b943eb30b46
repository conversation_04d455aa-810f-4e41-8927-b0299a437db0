# Docker Setup Guide for Process Software Project

This guide provides step-by-step instructions for setting up the Process Software project using Docker. Follow these instructions to get the development environment up and running quickly.

## Prerequisites

- [Docker](https://docs.docker.com/get-docker/) (version 20.10.0 or higher)
- [Docker Compose](https://docs.docker.com/compose/install/) (version 2.0.0 or higher)
- Git (for cloning the repository)
- Database backup file (if available)

## Step 1: Clone the Repository

```bash
git clone https://github.com/tvarit-foggy/process_sw.git
cd process_sw
```

## Step 2: Configure Environment Variables

The project uses environment variables for configuration. A sample `.env` file is provided in the repository.

```bash
# Ensure the .env file exists
cp .env.example .env  # If .env.example exists
```

Make sure the `.env` file contains the following variables:

```
NODE_ENV=development
MONGO_INITDB_DATABASE=tvarit

# PostgreSQL credentials
POSTGRES_DB=tvarit
POSTGRES_USER=tvarit_us
POSTGRES_PASSWORD=kjhagfT6sfer

# Database URIs
MONGO_URI=mongodb://mongo:27017/tvarit
DB_HOST=postgres
DB_PORT=5432

# JWT Secret (change this in production)
JWT_SECRET=your_jwt_secret_key
```

## Step 3: Update Docker Compose Configuration

If you encounter port conflicts, you may need to modify the `docker-compose.yml` file to use different ports. By default, the project uses:

- Backend: 8081:5000
- MongoDB: 27019:27017
- PostgreSQL: 5433:5432
- Redis: 6379:6379

## Step 4: Build and Start Docker Containers

```bash
# Build and start all containers in detached mode
docker-compose up -d
```

This command will:
1. Build the backend container
2. Pull the MongoDB, PostgreSQL, and Redis images
3. Create and start all containers
4. Create necessary volumes for data persistence

## Step 5: Check Container Status

```bash
# Check if all containers are running
docker-compose ps
```

You should see all containers (backend, mongo, postgres, redis) with status "Up".

## Step 6: Import Database (if you have a backup)

If you have a database backup file (e.g., `backup.sql`), you can import it into the PostgreSQL container:

```bash
# Copy the backup file to the PostgreSQL container
docker cp backup.sql process_sw-postgres-1:/tmp/

# Import the backup into the PostgreSQL database
docker exec -it process_sw-postgres-1 bash -c "psql -U tvarit_us -d tvarit -f /tmp/backup.sql"
```

## Step 7: Verify Database Import

Check if the database tables were created successfully:

```bash
# List all tables in the PostgreSQL database
docker exec -it process_sw-postgres-1 bash -c "psql -U tvarit_us -d tvarit -c '\dt'"
```

You should see a list of tables including users, tenants, workflows, etc.

## Step 8: Restart Backend Container

After importing the database, restart the backend container to ensure it connects to the updated database:

```bash
docker-compose restart backend
```

## Step 9: Access the Application

The application should now be accessible at:

- Frontend/Backend: http://localhost:8081

## Step 10: Login to the Application

Use the default credentials (if available) or create a new user through the API.

## Troubleshooting

### Database Connection Issues

If the backend can't connect to the database, check the logs:

```bash
docker-compose logs backend
```

Look for database connection errors and ensure the environment variables are correctly set.

### Port Conflicts

If you encounter port conflicts, modify the `docker-compose.yml` file to use different ports:

```yaml
services:
  backend:
    ports:
      - "8082:5000"  # Change 8081 to another port
```

### Container Not Starting

If a container fails to start, check its logs:

```bash
docker-compose logs <container_name>
```

Replace `<container_name>` with one of: backend, mongo, postgres, redis.

## Common Commands

### Start Containers

```bash
docker-compose up -d
```

### Stop Containers

```bash
docker-compose down
```

### Restart a Specific Container

```bash
docker-compose restart <container_name>
```

### View Container Logs

```bash
docker-compose logs <container_name>
```

### Access Container Shell

```bash
docker exec -it <container_name> bash
```

### Access PostgreSQL CLI

```bash
docker exec -it process_sw-postgres-1 psql -U tvarit_us -d tvarit
```

### Access MongoDB CLI

```bash
docker exec -it process_sw-mongo-1 mongosh
```

## Data Persistence

The Docker setup uses volumes to persist data:
- `mongo_data`: MongoDB data
- `postgres_data`: PostgreSQL data

These volumes ensure your data is preserved even if the containers are removed.

## Development Workflow

For development, the backend code is mounted as a volume, so changes to the code will be reflected in the container. However, you may need to restart the backend container to apply changes:

```bash
docker-compose restart backend
```
