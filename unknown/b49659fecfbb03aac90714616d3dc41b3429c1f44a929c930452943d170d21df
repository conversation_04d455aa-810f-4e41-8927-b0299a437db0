import { DataTypes, Model } from 'sequelize';
import { sequelize } from '../database/dbConnection.js'; // Import the Sequelize instance

class Parameters extends Model {}

Parameters.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false,  // Ensuring the name field is required
    },
    // code: {
    //   type: DataTypes.STRING(255),
    //   allowNull: false,  // Ensuring the code field is required
    //   unique: true,  // Optionally, enforce that 'code' is unique
    // },
    created_at: {
      type: DataTypes.DATE,
      defaultValue: DataTypes.NOW,
    },
    updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
    }
  },
  {
    sequelize, 
    modelName: 'Parameters',
    tableName: 'parameters',  // The table name in the database
    timestamps: false,  // Enable timestamps for createdAt and updatedAt
    underscored: true, // Use snake_case for column names in the database
  }
);

export default Parameters;
