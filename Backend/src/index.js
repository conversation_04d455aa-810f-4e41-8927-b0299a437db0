import {app} from './server/app.js'
import {connectDatabase, connectMongoDB} from './server/database/dbConnection.js'
import { runKpiTrackingSyncManually, startKpiTrackingCronJob } from './server/cron/kpiTrackingSync.js'
import 'dotenv/config'

const port = process.env.PORT || 5001
try {
    await connectDatabase()
} catch (error) {
    console.log('Database connection failed', error); 
}
try {
    await connectMongoDB()
} catch (error) {
    console.log('Database connection failed', error); 
}

// Start the KPI tracking cron job
// if (process.env.NODE_ENV === 'production') {
    // startKpiTrackingCronJob();
    // runKpiTrackingSyncManually();
// }

app.get('*', (req, res) => {
   // res.sendFile(path.join(__dirname, 'public', 'index.html'));
});

  
app.listen(port,(err)=>{
    if(err){
        console.log('error while listening on port :', err);
    }else{
        console.log("app is listening on port",port)
    }
})
