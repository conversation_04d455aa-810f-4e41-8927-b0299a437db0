import express from 'express';
import {authenticateToken} from '../middlewares/jwt.js'
import { createView ,deleteView ,getPannelTypes , getViewById , updateView, saveIndividualPanel, removeIndividualPanel} from '../controllers/view.controllers.js';
const router = express.Router();

router.post('/create-view',authenticateToken , createView);
router.delete('/:id/:type', authenticateToken, deleteView);
router.get('/pannel-types', authenticateToken, getPannelTypes);
router.get('/:viewId',authenticateToken, getViewById);
router.put('/:viewId',authenticateToken, updateView);
export default router;

// Individual panel operations
router.post('/panel/save', authenticateToken, saveIndividualPanel);
router.delete('/panel/remove/:panelId', authenticateToken, removeIndividualPanel);
