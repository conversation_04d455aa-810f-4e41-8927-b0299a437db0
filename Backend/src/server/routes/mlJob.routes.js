import { Router } from "express";
const router =Router()

// ********Import the controllers ******

import {uploadMljob,getMljobData,getMljobDataAll} from '../controllers/mljob.controller.js'


// ********Define path of controllers ******

router.route('/:workflowId').post(uploadMljob)
router.route('/jobdata/:workflowId/:mlJobId?').get(getMljobData)
router.route('/data/:workflowId').get(getMljobDataAll)

// router.route('/getcsv/:csvId').get(getFile)
// router.route('/list').get(getFileList)

export default router