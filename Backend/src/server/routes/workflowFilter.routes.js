import express from 'express';
import {authenticateToken} from '../middlewares/jwt.js'
import { workflowFilterList ,workflowFilterDetails,createFilter,updateFilterName,workflowStoredFilter} from '../controllers/workflowFilter.controllers.js';
const router = express.Router();


// router.post('/create',authenticateToken, createFolder);
router.get('/',authenticateToken, workflowFilterList);
router.get('/:id',authenticateToken, workflowFilterDetails);
router.post('/create-filter',authenticateToken, createFilter)
router.put('/update-filter-name/:id',authenticateToken, updateFilterName)
router.get('/workflow-filter/:id', workflowStoredFilter)
export default router;