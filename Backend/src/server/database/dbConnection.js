import Sequelize from 'sequelize'
import config  from '../config/local.js';
import User from '../models/user.model.js';
import mongoose from 'mongoose';

const sequelize = new Sequelize(config.postgres.database, config.postgres.user, config.postgres.password, {
  host: config.postgres.host,
  dialect: 'postgres',
  // logging: console.log,
  pool: {
    max: config.postgres.max,
    min: 0,
    acquire: 30000,
    idle: config.postgres.idleTimeoutMillis
  },
  dialectOptions: {
    requestTimeout: 3000
  },
  // operatorsAliases: false
});

async function connectDatabase(){
    try {
      console.log('config.postgres.host-- connecting',config.postgres.host);
      await sequelize.authenticate()
      // await sequelize.sync({ alter: true });
      console.log('Database Connection has been established successfully.');
    } catch (error) {
      console.error('Unable to connect to the database:', error);
      process.exit(1);
    }
  
  }

sequelize.Op = Sequelize.Op;
const sequelizer= sequelize;

async function connectMongoDB(){
  try {
    mongoose.connect(process.env.MONGO_URI)
    // await sequelize.sync({ alter: true });
    console.log('MongoDB Database Connection has been established successfully.');
  } catch (error) {
    console.error('Unable to connect to the MongoDB database:', error);
    process.exit(1);
  }

}



export {connectMongoDB,connectDatabase,sequelize,sequelizer} ;