// utils/mailer.js
import nodemailer from 'nodemailer';
import 'dotenv/config'
import fs from 'fs';
import path from 'path';
import ejs from 'ejs';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const emailTemplatePath = path.join(__dirname, './templates/emailOTPVerification.html');
const emailOTPTemplate = fs.readFileSync(emailTemplatePath, 'utf8');

const forgetPasswordEmail = path.join(__dirname, './templates/forgetPasswordOTP.html');
const forgetPasswordOTPTemplate = fs.readFileSync(forgetPasswordEmail, 'utf8');
const transporter = nodemailer.createTransport({
  host: process.env.SMTP_SENDER_HOST,
  port: 587, 
  secure: false, 
  auth: {
      user: process.env.SMTP_SENDER_EMAIL,
      pass: process.env.SMTP_SENDER_PASSWORD
  }
});

export const sendOtpEmail = async (to, otp ,user ,type = 'login') => {
console.log('type :', type);
    try {
      const subject =
      type == 'forgot-password'
        ? 'Tvarit Password Reset OTP Code'
        : 'Tvarit Login OTP Code';

        const templatePath =
        type == 'forgot-password'
          ? forgetPasswordEmail
          : emailTemplatePath;

        const template =
          type == 'forgot-password'
            ? forgetPasswordOTPTemplate
            : emailOTPTemplate;

      const htmlContent = await ejs.renderFile(templatePath, {
        first_name: user.first_name,
        otp,                          // the dynamic OTP
        headerHtml: template
      });

        await transporter.sendMail({
          from: `"Tvarit" <${process.env.SMTP_SENDER_EMAIL}>`,
          to:to,
          subject: subject,
          html: htmlContent,
        });
    } catch (error) {
    console.log('error ', error);
        
    }
};
