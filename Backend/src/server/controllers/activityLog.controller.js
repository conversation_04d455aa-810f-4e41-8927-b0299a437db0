import { sequelize } from "../database/dbConnection.js";
import { Sequelize, Op }from "sequelize";
import ActivityLog from "../models/activityLogs.model.js";
import User from "../models/user.model.js";
import Tenants from "../models/tenants.model.js";

export const addLog = async (req, res) => {
  try {
      const { last_used, last_login } = req.body;
      const user_id = req.user.id;

      if (!user_id) {
          return res.status(400).json({ error: "user_id is required" });
      }

      // Check if the user exists and if onboarding is completed
      const user = await User.findOne({ where: { id: user_id } });
      console.log('userrr', user)

      if (!user) {
          return res.status(404).json({ error: "User not found" });
      }

      if (!user.onboardingCompleted) {
          return res.status(403).json({ error: "Onboarding not completed. Cannot save activity log." });
      }

      // Prepare the new entry object
      const newEntry = { user_id };

      if (last_used) newEntry.last_used = last_used;
      if (last_login) newEntry.last_login = last_login;

      const activityLog = new ActivityLog(newEntry);
      await activityLog.save();

      return res.status(201).json({
          message: "Activity log created successfully",
          data: activityLog,
      });
  } catch (error) {
      console.error("Error creating activity log:", error.message);
      return res.status(500).json({ error: "Failed to create activity log" });
  }
};

export const UsersActivity = async (req, res) => {
    try {
      const { date, range, viewType, tenants } = req.body; // `date` is for a specific day; `range` is for multiple days
      let startOfDay, endOfDay, daysInRange = 1;
  
      if (date) {
        // Filter for a specific date
        startOfDay = new Date(date);
        endOfDay = new Date(date);
        endOfDay.setHours(23, 59, 59, 999);
      } else if (range && range.start && range.end) {
        // Filter for a date range
        startOfDay = new Date(range.start);
        startOfDay.setHours(0, 0, 0, 0);
        
        endOfDay = new Date(range.end);
        endOfDay.setHours(23, 59, 59, 999);

        const diffInMs = endOfDay - startOfDay;
        daysInRange = Math.ceil(diffInMs / (1000 * 60 * 60 * 24)); // Calculate days in the range
      } else {
        return res.status(400).json({ error: "Invalid date or range provided." });
      }
  
      const activityLogs = await ActivityLog.aggregate([
        {
          $match: {
            last_used: {
              $gte: startOfDay,
              $lte: endOfDay,
            },
          },
        },
        {
          $group: {
            _id: "$user_id",
            logs: { $push: "$last_used" },
          },
        },
      ]);
  
      if (!activityLogs.length) {
        return res
          .status(404)
          .json({ error: "No activity logs found for the specified date(s)." });
      }

      let dateFormat;
      switch (viewType) {
        case "daily":
          dateFormat = "%Y-%m-%d";
          break;
        case "weekly":
          dateFormat = "%Y-%U"; // Week number in year
          break;
        case "monthly":
          dateFormat = "%Y-%m";
          break;
        default:
          return res.status(400).json({ error: "Invalid viewType provided." });
      }

      const activityLogsByViewType = await ActivityLog.aggregate([
        {
          $match: {
            last_used: {
              $gte: startOfDay,
              $lte: endOfDay,
            },
          },
        },
        {
          $group: {
            _id: {
              user_id: "$user_id",
              period: { $dateToString: { format: dateFormat, date: "$last_used" } },
            },
            logs: { $push: "$last_used" },
          },
        },
      ]);

      const aggregatedData = {};
      activityLogsByViewType.forEach((log) => {
        const { period } = log._id;
        if (!aggregatedData[period]) {
          aggregatedData[period] = 0;
        }
  
        let totalMinutesSpent = 0;
        let previousEntry = null;
  
        log.logs.forEach((timestamp) => {
          if (previousEntry) {
            const timeDiff = Math.min(
              (new Date(timestamp) - new Date(previousEntry)) / (1000 * 60),
              5 // Cap at 5 minutes
            );
            totalMinutesSpent += timeDiff;
          }
          previousEntry = timestamp;
        });
  
        aggregatedData[period] += totalMinutesSpent;
      });

      const periods = generatePeriods(startOfDay, endOfDay, viewType);

      // Merge periods with aggregated data
      const totalTimeEachDay = periods.map((period) => ({
        day: period,
        totalTime: aggregatedData[period] ? aggregatedData[period].toFixed(2) : "0.00",
      })).sort((a, b) => a.day.localeCompare(b.day));
      console.log('totalTimeEachDay', totalTimeEachDay)
  
      // Fetch user details from SQL
      const userIds = activityLogs.map((log) => log._id);
      let whereCondition = {};

      // Add `userIds` condition if it's provided
      if (userIds && userIds.length) {
        whereCondition.id = userIds;
      }

      // Add `tenants` condition if it's provided and has length
      if (tenants && tenants.length) {
        console.log('tenants', tenants)
        whereCondition.tenant_id = {
          [Op.in]: tenants, 
        };
      }
      console.log('whereCondition', whereCondition)
      const users = await User.findAll({ where: whereCondition });
      console.log('users', users)

      if (!users.length) {
        return res.status(200).json({
          results: [],
          totalTimeEachDay: []
        });
      }
      const userMap = users.reduce((acc, user) => {
        acc[user.id] = user.first_name;
        return acc;
      }, {});
  
      // Fetch workflow counts from SQL
      const workflows = await sequelize.query(
        `
        SELECT user_id, COUNT(*) as workflow_count
        FROM workflows
        WHERE created_at BETWEEN :startOfDay AND :endOfDay
        AND user_id IN (:userIds)
        GROUP BY user_id
        `,
        {
          replacements: { startOfDay, endOfDay, userIds },
          type: Sequelize.QueryTypes.SELECT,
        }
      );
  
      const workflowMap = workflows.reduce((acc, workflow) => {
        acc[workflow.user_id] = workflow.workflow_count;
        return acc;
      }, {});
  
      // Calculate total and average time spent per user
      // let results = activityLogs.map((log) => {
        let results = activityLogs.filter(log => userMap[log._id]).map((log) => {
        const userId = log._id;
        const userName = userMap[userId] || "Unknown User";
        const workflowCount = workflowMap[userId] || 0;
  
        let totalMinutesSpent = 0;
        let previousEntry = null;
  
        log.logs.forEach((timestamp) => {
          if (previousEntry) {
            const timeDiff = Math.min(
              (new Date(timestamp) - new Date(previousEntry)) / (1000 * 60), // Time in minutes
              5 // Cap at 5 minutes
            );
            totalMinutesSpent += timeDiff;
          }
          previousEntry = timestamp;
        });

        const averageTimeSpent = totalMinutesSpent / daysInRange;
  
        return {
          user_id: userId,
          user_name: userName,
          totalMinutesSpent: totalMinutesSpent ? totalMinutesSpent.toFixed(2) : totalMinutesSpent,
          averageTimeSpent: averageTimeSpent ? averageTimeSpent.toFixed(2) : averageTimeSpent,
          workflowCount,
          // totalTimeEachDay
        };
      });

      if(results){
        results = results.sort((a, b) => b.totalMinutesSpent - a.totalMinutesSpent);
      }
  
      res.status(200).json({ results, totalTimeEachDay });
    } catch (error) {
      console.error("Error calculating time spent:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  };

  const generatePeriods = (start, end, type) => {
    const periods = [];
    let current = new Date(start);
    
    while (current <= end) {
      switch (type) {
        case "daily":
          periods.push(current.toISOString().slice(0, 10)); // YYYY-MM-DD
          current.setDate(current.getDate() + 1);
          break;
        case "weekly":
          const year = current.getFullYear();
          const week = Math.ceil(
            (current - new Date(year, 0, 1)) / (7 * 24 * 60 * 60 * 1000)
          );
          periods.push(`${year}-${week.toString().padStart(2, "0")}`);
          current.setDate(current.getDate() + 7);
          break;
        case "monthly":
          periods.push(current.toISOString().slice(0, 7)); // YYYY-MM
          current.setMonth(current.getMonth() + 1);
          break;
      }
    }
    return periods;
  };

  export const UsersDailyTimeSpent = async (req, res) => {
    try {
      const { range, user_id, viewType } = req.body;
  
      if (!range || !range.start || !range.end) {
        return res.status(400).json({ error: "Invalid range provided." });
      }
  
      if (!["daily", "weekly", "monthly"].includes(viewType)) {
        return res.status(400).json({ error: "Invalid viewType provided." });
      }
  
      const startOfDay = new Date(range.start);
      startOfDay.setHours(0, 0, 0, 0);
      const endOfDay = new Date(range.end);
      endOfDay.setHours(23, 59, 59, 999);
  
      // Set date format based on viewType
      let dateFormat;
      switch (viewType) {
        case "daily":
          dateFormat = "%Y-%m-%d"; // Daily format
          break;
        case "weekly":
          dateFormat = "%Y-%U"; // Weekly format (year + week number)
          break;
        case "monthly":
          dateFormat = "%Y-%m"; // Monthly format (year + month)
          break;
      }
  
      // Fetch activity logs grouped by the selected viewType
      const activityLogs = await ActivityLog.aggregate([
        {
          $match: {
            user_id,
            last_used: {
              $gte: startOfDay,
              $lte: endOfDay,
            },
          },
        },
        {
          $group: {
            _id: {
              user_id: "$user_id",
              period: { $dateToString: { format: dateFormat, date: "$last_used" } },
            },
            logs: { $push: "$last_used" },
          },
        },
      ]);
  
      // Process activity logs
      const userTimeSpentData = {};
      activityLogs.forEach((log) => {
        const { user_id, period } = log._id;
  
        if (!userTimeSpentData[user_id]) userTimeSpentData[user_id] = {};
  
        let totalMinutesSpent = 0;
        let previousEntry = null;
  
        log.logs.forEach((timestamp) => {
          if (previousEntry) {
            const timeDiff = Math.min(
              (new Date(timestamp) - new Date(previousEntry)) / (1000 * 60), // Time in minutes
              5 // Cap at 5 minutes
            );
            totalMinutesSpent += timeDiff;
          }
          previousEntry = timestamp;
        });
  
        userTimeSpentData[user_id][period] = totalMinutesSpent;
      });
  
      const allPeriods = generatePeriods(startOfDay, endOfDay, viewType);

      // Fill missing periods with zero time spent
      const filledData = allPeriods.map((period) => ({
        day: period,
        totalTime: userTimeSpentData[user_id]?.[period] || 0,
      }));
  
      // Fetch all users for user names
      const allUsers = await User.findAll();
      const userMap = allUsers.reduce((acc, user) => {
        acc[user.id] = user.first_name;
        return acc;
      }, {});
  
      // Prepare response
      const results = {
        user_id,
        user_name: userMap[user_id] || "Unknown User",
        dailyData: filledData.map((data) => ({
          day: data.day,
          totalTime: data.totalTime.toFixed(2),
        })),
      };
  
      res.status(200).json({ results });
    } catch (error) {
      console.error("Error calculating user's time spent:", error);
      res.status(500).json({ error: "Internal server error" });
    }
  };
  

export const getTenants = async (req, res) => {
  try {
    let tenants = await Tenants.findAll();

    return res.status(201).json({
      message: 'Tenants fetched successfully',
      data: tenants
  });
  } catch (error) {
    console.error("error occured", error);
    res.status(500).json({ error: "Internal server error" });
  }
};
  
  
