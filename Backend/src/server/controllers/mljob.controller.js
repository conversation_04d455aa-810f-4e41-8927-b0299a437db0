import { generateResponse } from "../utils/commonResponse.js";
import CSVFilesSchema from "../models/file.model.js"
import <PERSON><PERSON><PERSON><PERSON> from "../models/MLjobs.model.js";
import MLJobData from "../models/MLJobData.model.js";
import mongoose from "mongoose";
import Workflow from "../models/workflow.model.js";

const uploadMljob = async (req, res) => {
    const { workflowData, execution_id } = req.body;
    const {workflowId} = req.params;
    const { status } = req.body
    console.log('--flow result---',status,workflowId)
    if(!workflowData || !workflowId || !status){
        return generateResponse(res, 400, 'Not get all required parameters')
    }
    try {
        const jobQuery = execution_id
          ? { _id: execution_id }
          : { workflow_id: workflowId };
        const updatedJob = await MLJob.findOneAndUpdate(
          jobQuery,
          { status: status },
          { new: true }
        ).sort({ _id: -1 });
        if (!updatedJob) {
            return generateResponse(res, 404, 'MLJob not found');
        }

        if (updatedJob.file_id) {
            await CSVFilesSchema.update(
                { clustering_data_response: status === 'completed' ? 'completed' : 'failed' },
                { where: { csv_id: updatedJob.file_id } }
            );
        }

        const mlJobData = new MLJobData({
            ml_job_id: updatedJob._id,
            result: workflowData,
            workflow_id: workflowId
        });

        await mlJobData.save();
        return generateResponse(res, 200, 'MLJobData entry created successfully:')
    } catch (error) {
        console.log("error",error)
        return generateResponse(res, 500, 'Error completing MLJob and saving result:')
    }

}

const getMljobData = async (req, res) => {
  const {workflowId} = req.params;
  const {mlJobId} = req.params;

  try {
    if(mlJobId){
      
      const mlJobAvaibality = await MLJob.findOne({ _id: new mongoose.Types.ObjectId(mlJobId) })
      .sort({ _id: -1 });
      if(!mlJobAvaibality){
          return generateResponse(res, 404, 'ML job not found:')
      }
      console.log('mlJobAvaibality', mlJobAvaibality)
      let mlJob = await MLJobData.find({ ml_job_id: mlJobId})
      .sort({ _id: -1 }) // Sort by `_id` in descending order to get the latest entry
      .limit(1) 
      // console.log('mlJobAvaibality.created_at', mlJob[0].result[0])
      console.log('mlJobbbbbb', mlJob)

      if(mlJobAvaibality.status =='in_progress' && mlJob.length == 0 ){
          return generateResponse(res, 200, 'MLJobData fetched successfully:', {},mlJobAvaibality.status)
      }else if(mlJobAvaibality.status =='in_progress' && mlJob.length > 0 && mlJob[0]?.result[0]?.message){
          mlJobAvaibality.status  = 'failed'
      }else if(mlJobAvaibality.status =='in_progress'){
          mlJobAvaibality.status  = 'completed'
      }

      if (!mlJob) {
          return generateResponse(res, 404, 'ML job not found:')
      }
      const columnsOrder = await Workflow.findOne({
          where:{id:workflowId},
          attributes: ['columns_order']
      })

      let data = mlJob[0]?.result;
      data = {
        ...data,
        mljobId: mlJob[0]?.id,
        columns: columnsOrder,
        settings: mlJobAvaibality.settings,
        actual_settings: mlJobAvaibality?.actual_settings
      };
      return generateResponse(res, 200, 'MLJobData fetched successfully:', data,mlJobAvaibality.status)
    }
    else{
      const mlJobAvaibality = await MLJob.findOne({ workflow_id: workflowId })
      .sort({ _id: -1 });
      if(!mlJobAvaibality){
          return generateResponse(res, 404, 'ML job not found:')
      }
      let mlJob = await MLJobData.find({ workflow_id: workflowId,'_id':{$gte:mlJobAvaibality._id}})
      .sort({ _id: -1 }) // Sort by `_id` in descending order to get the latest entry
      .limit(1) 
      // console.log('mlJobAvaibality.created_at', mlJob[0].result[0])

      if(mlJobAvaibality.status =='in_progress' && mlJob.length == 0 ){
          return generateResponse(res, 200, 'MLJobData fetched successfully:', {},mlJobAvaibality.status)
      }else if(mlJobAvaibality.status =='in_progress' && mlJob.length > 0 && mlJob[0]?.result[0]?.message){
          mlJobAvaibality.status  = 'failed'
      }else if(mlJobAvaibality.status =='in_progress'){
          mlJobAvaibality.status  = 'completed'
      }
      // const mlJob = await MLJobData.find({ workflow_id: workflowId })
      // .sort({ _id: -1 }) // Sort by `_id` in descending order to get the latest entry
      // .limit(1)
      // Get the most recent document only
      // .populate({
      //       path: 'ml_job_id', // Reference the ml_job_id field in MLJobData
      //       model: 'MLJob'     // Populate it with data from the MLJob model
      //     });
      
      if (!mlJob) {
          return generateResponse(res, 404, 'ML job not found:')
      }
      const columnsOrder = await Workflow.findOne({
          where:{id:workflowId},
          attributes: ['columns_order']
      })
      // console.log('columnsOrder :', columnsOrder);
      // mlJob[0].columns=columnsOrder
      let data = mlJob[0]?.result;
      data = {
        ...data,
        mljobId: mlJob[0]?.id,
        columns: columnsOrder,
        settings: mlJobAvaibality.settings
      };
      return generateResponse(res, 200, 'MLJobData fetched successfully:', data,mlJobAvaibality.status)
    }

  } catch (error) {
      console.log('error',error)
      return generateResponse(res, 500, 'Error fetching ML job:')
  }
}

const getMljobDataAll= async (req, res) => {
    const {workflowId} = req.params;
    
    try {
        const mlJob = await MLJobData.find({}).sort({_id:-1}).limit(2);
    
        if (!mlJob) {
            return generateResponse(res, 404, 'ML job not found:')
        }
        return generateResponse(res, 200, 'MLJobData fetched successfully:', mlJob)
    } catch (error) {
        console.log('error',error)
        return generateResponse(res, 500, 'Error fetching ML job:')
    }
    
    }

export {
    uploadMljob,
    getMljobData,
    getMljobDataAll
}