import { generateResponse } from "../utils/commonResponse.js";
import User from '../models/user.model.js'
import { sequelizer } from '../database/dbConnection.js'
import { generateToken } from "../middlewares/jwt.js";
import ActivityLog from "../models/activityLogs.model.js";
import Roles from "../models/roles.model.js";
import speakeasy from 'speakeasy';
import QRCode from 'qrcode';
import bcrypt from 'bcrypt';
import crypto from 'crypto';
import otpStore from "../utils/otpStore.js";
import { sendOtpEmail } from '../utils/mailer.js';
// Function to encrypt the secret (using AES-256-GCM)
const encryptSecret = (plainSecret) => {
  // Use a secure encryption key stored in environment variables
  const encryptionKey = process.env.SECRET_ENCRYPTION_KEY;
  const iv = crypto.randomBytes(16);
  const cipher = crypto.createCipheriv('aes-256-gcm', Buffer.from(encryptionKey, 'hex'), iv);

  let encrypted = cipher.update(plainSecret, 'utf8', 'hex');
  encrypted += cipher.final('hex');

  // Get the auth tag
  const authTag = cipher.getAuthTag().toString('hex');

  // Return IV + encrypted data + auth tag
  return iv.toString('hex') + ':' + encrypted + ':' + authTag;
};

// Function to decrypt the secret
const decryptSecret = (encryptedData) => {
  const encryptionKey = process.env.SECRET_ENCRYPTION_KEY;
  const parts = encryptedData.split(':');

  if (parts.length !== 3) {
    throw new Error('Invalid encrypted data format');
  }

  const iv = Buffer.from(parts[0], 'hex');
  const encrypted = parts[1];
  const authTag = Buffer.from(parts[2], 'hex');

  const decipher = crypto.createDecipheriv('aes-256-gcm', Buffer.from(encryptionKey, 'hex'), iv);
  decipher.setAuthTag(authTag);

  let decrypted = decipher.update(encrypted, 'hex', 'utf8');
  decrypted += decipher.final('utf8');

  return decrypted;
};

// New API to encrypt passwords
export const encryptPassword = async (req, res) => {
  try {
    const { password } = req.body;

    if (!password) {
      return generateResponse(res, 400, 'Password is required');
    }

    // Use bcrypt to hash the password
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(password, saltRounds);

    return generateResponse(res, 200, 'Password encrypted successfully', {
      encryptedPassword: hashedPassword
    });
  } catch (error) {
    console.error('Error encrypting password:', error);
    return generateResponse(res, 500, 'Failed to encrypt password');
  }
};

const loginUser = async (req, res) => {
  const { email, password } = req.body;
  if (!email) {
    return generateResponse(res, 400, 'email is required')
  }
  if (!password) {
    return generateResponse(res, 400, 'password is required')
  }

  try {
    const existingUser = await User.findOne({
      attributes: ['id', 'first_name', 'last_name', 'email', 'password_hash', 'onboarding', 'tenant_id', 'selected_systems', 'two_factor_enabled', 'two_factor_secret', 'email_verification_enabled', 'is_first_login'],
      where: {
        email: email
      },
      include: [
        {
          model: Roles,
          as: 'roles',
          attributes: ['name', 'alias', 'rank']
        },
      ]
    });

    if (!existingUser) {
      return generateResponse(res, 404, 'user not found')
    }

    const passwordMatch = await bcrypt.compare(password, existingUser.get('password_hash'));
    console.log('passwordMatch', passwordMatch)

    if (!passwordMatch) {
      return generateResponse(res, 404, 'email or password is invalid')
    }

    // Check if 2FA is enabled for this user
    let is2FAAuthenticationEnabled = process.env.IS_2FA_AUTHENTICATION_ENABLE == 'true' ? true : false
    let isEmailVerificationEnabled = process.env.IS_EMAIL_AUTHENTICATION_ENABLE == 'true' ? true : false

    if (is2FAAuthenticationEnabled && existingUser.get('two_factor_enabled')) {
      // Return a special response indicating 2FA is required
      return generateResponse(res, 200, 'Two-factor authentication required', {
        requiresTwoFactor: true,
        userId: existingUser.get('id'),
        first_name: existingUser.get('first_name'),
        email:existingUser.get('email')
      });
    }
    if(isEmailVerificationEnabled && existingUser.get('email_verification_enabled') && !existingUser.get('is_first_login')){
      return generateResponse(res, 200, 'Email OTP verification required', {
        requiresEmailVerification: true,
        userId: existingUser.get('id'),
        first_name: existingUser.get('first_name'),
        email:existingUser.get('email'),
      });
    }

    // If 2FA is not enabled, proceed with normal login
    const userData = {
      first_name: existingUser.get('first_name'),
      last_name: existingUser.get('last_name'),
      email: existingUser.get('email'),
      id: existingUser.get('id'),
      onboarding: existingUser.get('onboarding'),
      tenant_id: existingUser.get('tenant_id'),
      selected_systems: existingUser.get('selected_systems'),
      role: existingUser.get('roles'),
      is_first_login: is2FAAuthenticationEnabled ? (existingUser.get('is_first_login') || false) : false,
      requiresEmailVerification : isEmailVerificationEnabled ? (existingUser.get('email_verification_enabled')) :false
    }

    const token = generateToken(userData)
    if(!token){
      return generateResponse(res, 404, 'unable to generate token')
    }
    userData.token = token

    const newEntry = { user_id: userData.id, last_login: new Date() };

    const activityLog = new ActivityLog(newEntry);
    await activityLog.save();
    res.cookie('token', token, { httpOnly: false, secure: false });
    return generateResponse(res, 200, 'User logged In', userData)

  } catch (error) {
    console.error('Error fetching users:', error);
    return generateResponse(res, 500, 'Internal server error')
  }
}

const updateUserOnboardingStatus = async (req, res) =>{
  const { status } = req.body;
  const {id} = req.user
  const {userId} = req.params
  if(!status){
    return generateResponse(res, 400, 'Status is required')
  }
  if(id != userId){
    return generateResponse(res, 401, 'You are not authorized to update the status of this user.')
  }
  try {
    const updatedUser = await User.update(
      { onboarding: status },
      {
        where: { id: id },
        returning: true,
      }
    )
    if(!updatedUser){
      return generateResponse(res, 404, 'User with the provided ID not found')
    }

    let user={
      email: updatedUser[1][0].email,
      is_active: updatedUser[1][0].is_active,
      onboarding: updatedUser[1][0].onboarding
    }
    return generateResponse(res, 200, 'User onboarding status updated successfully',user)

  } catch (error) {
  console.log('error :', error);
  return generateResponse(res, 500, 'Internal server error')
  }

}

const updateUserSelectedSystems = async (req, res) =>{
  const systems = Array.isArray(req.body.selected_systems) ? req.body.selected_systems: JSON.parse(req.body.selected_systems || '[]');
  let systemIds = [];
  if(systems && systems.length){
    systemIds = systems.map(item => item.systemId);
  }

  const {id} = req.params
  if(!systemIds){
    return generateResponse(res, 400, 'Selected systems are required')
  }
  try {
    const updatedUser = await User.update(
      { selected_systems: systemIds },
      {
        where: { id: id },
        returning: true,
      }
    )

    if(!updatedUser){
      return generateResponse(res, 404, 'User with the provided ID not found')
    }

    let user={
      email: updatedUser.email,
      selected_systems: updatedUser.selected_systems
    }
    return generateResponse(res, 200, 'User selected systems updated successfully',user)

  } catch (error) {
  console.log('error :', error);
  return generateResponse(res, 500, 'Internal server error')
  }
}

const usersList = async (req ,res) =>{
  try {

    const users = await User.findAll({
      attributes: ['id', 'first_name', 'last_name', 'email' , 'tenant_id'],
      where:{
        is_active : true,
        tenant_id: req.user.tenant_id
      },
      order: [['created_at', 'DESC']],
    })

    return generateResponse(res , 200 , 'Users list fetched successfully' , users)

  } catch (error) {
    return generateResponse(res, 500, 'Internal server error')
  }
}

export const setup2FA = async (req, res) => {
  try {
    const userId = req.user.id;

    // Generate a secret key
    const secret = speakeasy.generateSecret({
      length: 20,
      name: `Tvarit:${req.user.email}`
    });

    // Generate QR code
    const qrCodeUrl = await QRCode.toDataURL(secret.otpauth_url);

    // Encrypt the secret before storing
    const encryptedSecret = encryptSecret(secret.base32);

    // Store the encrypted secret temporarily
    await User.update(
      {
        temp_2fa_secret: encryptedSecret,
        temp_2fa_enabled: false
      },
      { where: { id: userId } }
    );

    return generateResponse(res, 200, 'Two-factor authentication setup initiated', {
      qrCode: qrCodeUrl,
      secret: secret.base32 // Send the original secret to the client for QR code
    });
  } catch (error) {
    console.error('Error setting up 2FA:', error);
    return generateResponse(res, 500, 'Failed to setup two-factor authentication');
  }
};

export const verify2FA = async (req, res) => {
  try {
    const { token, secret } = req.body;
    const userId = req.user.id;

    // Get the user
    const user = await User.findByPk(userId);
    if (!user) {
      return generateResponse(res, 404, 'User not found');
    }

    // If client provided the secret (during initial setup)
    if (secret) {
      // Verify the token against the provided secret
      const verified = speakeasy.totp.verify({
        secret: secret,
        encoding: 'base32',
        token: token
      });

      if (verified) {
        // Encrypt the secret before storing permanently
        const encryptedSecret = encryptSecret(secret);

        // Update user with verified 2FA and set is_first_login to false
        await User.update(
          {
            two_factor_secret: encryptedSecret,
            two_factor_enabled: true,
            temp_2fa_secret: null,
            temp_2fa_enabled: null,
            is_first_login: false
          },
          { where: { id: userId } }
        );

        return generateResponse(res, 200, 'Two-factor authentication verified and enabled');
      } else {
        return generateResponse(res, 400, 'Invalid verification code');
      }
    }
    // Using the temporary secret from the database
    else if (user.temp_2fa_secret) {
      return generateResponse(res, 400, 'Direct verification with temp secret not supported');
    } else {
      return generateResponse(res, 400, 'No secret provided for verification');
    }
  } catch (error) {
    console.error('Error verifying 2FA:', error);
    return generateResponse(res, 500, 'Failed to verify two-factor authentication');
  }
};

export const disable2FA = async (req, res) => {
  try {
    const userId = req.user.id;

    // Update user to disable 2FA
    await User.update(
      {
        two_factor_secret: null,
        two_factor_enabled: false,
        temp_2fa_secret: null,
        temp_2fa_enabled: null
      },
      { where: { id: userId } }
    );

    return generateResponse(res, 200, 'Two-factor authentication disabled successfully');
  } catch (error) {
    console.error('Error disabling 2FA:', error);
    return generateResponse(res, 500, 'Failed to disable two-factor authentication');
  }
};

export const get2FAStatus = async (req, res) => {
  try {
    const userId = req.user.id;

    // Get the user's 2FA status
    const user = await User.findByPk(userId, {
      attributes: ['two_factor_enabled']
    });

    if (!user) {
      return generateResponse(res, 404, 'User not found');
    }

    return generateResponse(res, 200, 'Two-factor authentication status retrieved', {
      isEnabled: user.two_factor_enabled || false
    });
  } catch (error) {
    console.error('Error getting 2FA status:', error);
    return generateResponse(res, 500, 'Failed to get two-factor authentication status');
  }
};

export const verifyLoginWith2FA = async (req, res) => {
  try {
    const { userId, token } = req.body;

    if (!userId || !token) {
      return generateResponse(res, 400, 'User ID and verification token are required');
    }

    // Get the user with their 2FA secret
    const user = await User.findOne({
      attributes: ['id', 'first_name', 'last_name', 'email', 'onboarding', 'tenant_id', 'selected_systems', 'two_factor_secret', 'two_factor_enabled', 'is_first_login'],
      where: { id: userId },
      include: [
        {
          model: Roles,
          as: 'roles',
          attributes: ['name', 'alias', 'rank']
        },
      ]
    });

    if (!user) {
      return generateResponse(res, 404, 'User not found');
    }

    if (!user.two_factor_enabled || !user.two_factor_secret) {
      return generateResponse(res, 400, 'Two-factor authentication is not enabled for this user');
    }

    const verified = await verifyTokenSecurely(token, user.two_factor_secret);

    if (!verified) {
      return generateResponse(res, 400, 'Invalid verification code');
    }

    // If verified, create user data and token
    const userData = {
      first_name: user.get('first_name'),
      last_name: user.get('last_name'),
      email: user.get('email'),
      id: user.get('id'),
      onboarding: user.get('onboarding'),
      tenant_id: user.get('tenant_id'),
      selected_systems: user.get('selected_systems'),
      role: user.get('roles'),
      is_first_login: user.get('is_first_login') || false
    };

    const jwtToken = generateToken(userData);
    if (!jwtToken) {
      return generateResponse(res, 404, 'Unable to generate token');
    }

    userData.token = jwtToken;

    // Log the activity
    const newEntry = { user_id: userData.id, last_login: new Date() };
    const activityLog = new ActivityLog(newEntry);
    await activityLog.save();

    // Set cookie and return response
    res.cookie('token', jwtToken, { httpOnly: false, secure: false });
    return generateResponse(res, 200, 'User logged in successfully', userData);

  } catch (error) {
    console.error('Error verifying 2FA during login:', error);
    return generateResponse(res, 500, 'Internal server error');
  }
};

// Helper function to securely verify token with encrypted secret
// This is a placeholder - you'll need to implement a secure approach
async function verifyTokenSecurely(token, encryptedSecret) {
  try {
    // Decrypt the secret
    const decryptedSecret = decryptSecret(encryptedSecret);

    // Verify the token
    return speakeasy.totp.verify({
      secret: decryptedSecret,
      encoding: 'base32',
      token: token
    });
  } catch (error) {
    console.error('Error verifying token:', error);
    return false;
  }
}

export const updateFirstLoginStatus = async (req, res) => {
  try {
    console.log("updateFirstLoginStatus called");
    const userId = req.user.id;
    console.log("User ID:", userId);

    // Update the user's is_first_login flag to false
    const result = await User.update(
      { is_first_login: false },
      { where: { id: userId } }
    );
    console.log("Update result:", result);

    // Verify the update
    const user = await User.findByPk(userId, {
      attributes: ['is_first_login']
    });
    console.log("User after update:", user ? user.get() : null);

    return generateResponse(res, 200, 'First login status updated successfully');
  } catch (error) {
    console.error('Error updating first login status:', error);
    return generateResponse(res, 500, 'Failed to update first login status');
  }
};

export const forgotPassword = async (req, res) => {
  try {
    const { email } = req.body;

    // Check if email is provided
    if (!email) {
      return generateResponse(res, 400, 'Email is required');
    }

    // Find user by email
    const user = await User.findOne({ where: { email } });
    console.log('user :', user);

    if (!user) {
      return generateResponse(res, 404, 'No user found with this email');
    }

    // Generate a secure token
    const otp = crypto.randomInt(100000, 999999).toString();
    otpStore.storeOtp(email, otp ,'forgot-password');
    try {
      const userData ={
        first_name : user?.first_name
      }
      await sendOtpEmail(email, otp ,userData , 'forgot-password');
      return generateResponse(res, 200, 'Password reset OTP sent to email');
    } catch (err) {
      return generateResponse(res, 500, 'Failed to Send Password reset OTP to email');
    }

  } catch (error) {
    console.error('Error in forgotPassword:', error);
    return generateResponse(res, 500, 'Server error while sending reset link');
  }
};

export const verifyForgotPasswordOtp = async (req, res) => {
  try {
    const { email, otp } = req.body;

    // Check if email and otp are provided
    if (!email || !otp) {
      return generateResponse(res, 400, 'Email and OTP are required');
    }

    // Verify OTP using the 'forgot-password' purpose
    const isValid = otpStore.verifyOtp(email, otp, 'forgot-password');

    if (!isValid) {
      return generateResponse(res, 401, 'Invalid or expired OTP');
    }

    return generateResponse(res, 200, 'OTP verified successfully');
  } catch (error) {
    console.error('Error in verifyForgotPasswordOtp:', error);
    return generateResponse(res, 500, 'Server error during OTP verification');
  }
};

export const changePassword = async (req, res) => {
  try {
    const { email, newPassword } = req.body;

    // Validate inputs
    if (!email || !newPassword) {
      return generateResponse(res, 400, 'Email and new password are required');
    }

    // Optional: Add password strength validation
    const hasMinLength = newPassword.length >= 8;
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(newPassword);

    if (!hasMinLength || !hasSpecialChar) {
      return generateResponse(
        res,
        400,
        'Password must be at least 8 characters long and include a special character'
      );
    }

    // Check if user exists
    const user = await User.findOne({ where: { email } });

    if (!user) {
      return generateResponse(res, 404, 'User not found');
    }

    // Hash new password
    const saltRounds = 10;
    const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

    // Update user password
    await User.update(
      { password_hash: hashedPassword },
      { where: { email } }
    );

    return generateResponse(res, 200, 'Password updated successfully');
  } catch (error) {
    console.error('Error changing password:', error);
    return generateResponse(res, 500, 'Server error while changing password');
  }
};



export {
  loginUser,
  updateUserOnboardingStatus,
  updateUserSelectedSystems,
  usersList,
}