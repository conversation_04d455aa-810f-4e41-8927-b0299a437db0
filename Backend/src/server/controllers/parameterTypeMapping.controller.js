import { sequelize } from "../database/dbConnection.js";
import { Sequelize, Op }from "sequelize";
import ParameterTypeMapping from "../models/parameter_type_mapping.model.js";
import Parameters from "../models/parameters.model.js";
import ParametersType from "../models/parametersType.model.js";

const parameterMapping = {
    fillingStartTime: 'Time',
    fillingStopTime: 'Time',
    cultureQuantity: 'Quantity&Volume',
    cultureAdditionTime: 'Time',
    rennetQuantity: 'Quantity&Volume',
    rennetAdditionTime: 'Time',
    coagulationStartTime: 'Time',
    coagulationStopTime: 'Time',
    emptyingStartTime: 'Time',
    emptyingStopTime: 'Time',
    cuttingStartTime: 'Time',
    sequenceNo: 'GenericIdentifiers',
    vatNo: 'GenericIdentifiers',
    recipeNo: 'GenericIdentifiers',
    milkQuantityInVat: 'Quantity&Volume',
    residenceTimeInVat: 'DerivedParameters',
    totalProcesstimeInVat: 'DerivedParameters',
    emptyingDuration: 'DerivedParameters',
    cookingDuration: 'DerivedParameters',
    coagulationDuration: 'DerivedParameters',
    temperatureInThermiser: 'Temperature',
    DateTime: 'Time',
    temperatureOfMilkInVat: 'Temperature',
    fillingRPM: 'Flow&Rate&Speed',
    cuttingRPM: 'Flow&Rate&Speed',
    intermittentCuttingRPM: 'Flow&Rate&Speed',
    cultureType: 'MaterialQuality'
  };

export async function addParameterMappings() {
    try {
        for (const [parameterName, parameterTypeName] of Object.entries(parameterMapping)) {
            const parameter = await Parameters.findOne({ where: { name: parameterName } });
            const parameterType = await ParametersType.findOne({ where: { name: parameterTypeName } });

            if (parameter && parameterType) {
                // Insert the mapping into the new table
                await ParameterTypeMapping.create({
                parameter_id: parameter.id,
                parameter_type_id: parameterType.id
                });
                console.log(`Mapping added: ${parameterName} - ${parameterTypeName}`);
            } else {
                console.error(`Missing mapping for: ${parameterName} - ${parameterTypeName}`);
            }
        }
    } catch (error) {
        console.error('Error adding parameter mappings:', error);
    }
}

export const getParameter = async (req, res) => {
    try {
        const { type } = req.params;

        const query = `
            SELECT p.name
            FROM parameters p
            WHERE p.id IN (
            SELECT ptm.parameter_id
            FROM parameter_type_mapping ptm
            WHERE ptm.parameter_type_id = (
                SELECT pt.id
                FROM parameters_type pt
                WHERE pt.name = :type
            )
            );
        `;

        const results = await sequelize.query(query, {
            replacements: { type },
            type: Sequelize.QueryTypes.SELECT,
        });
        return res.status(200).json({
            message: "Parameter fetched successfully.",
            data: results,
        });
    } catch (error) {
        console.error('Error adding parameter mappings:', error);
    }
}

export const fetchParametersByTypes = async (req, res) => {
    try {
        const { types } = req.body; // Expecting an array of types

        if (!Array.isArray(types) || types.length === 0) {
            return res.status(400).json({ message: "Invalid input: 'types' should be a non-empty array." });
        }

        const query = `
            SELECT DISTINCT p.name
            FROM parameters p
            JOIN parameter_type_mapping ptm ON p.id = ptm.parameter_id
            JOIN parameters_type pt ON ptm.parameter_type_id = pt.id
            WHERE pt.name IN (:types);
        `;

        const results = await sequelize.query(query, {
            replacements: { types },
            type: Sequelize.QueryTypes.SELECT,
        });

        return res.status(200).json({
            message: "Parameters fetched successfully.",
            data: results,
        });
    } catch (error) {
        console.error('Error fetching parameters by types:', error);
        return res.status(500).json({ message: 'Internal server error.' });
    }
}
  
