import { generateResponse } from "../utils/commonResponse.js";
import Materials from "../models/materials.model.js"
import qualityParameters from "../models/qualityParameters.model.js";
import MaterialQualityParameters from "../models/materialQualityParameters.model.js";
import product from "../models/products.model.js"
import Tenants from "../models/tenants.model.js";
import TenantProducts from "../models/tenantProducts.model.js";
import productMaterial from "../models/productMaterials.model.js"
import processes from "../models/processes.model.js"
import parameters from "../models/parameters.model.js"
import processParameters from "../models/processParameters.model.js"
import GlobalConfiguration from "../models/globalConfiguration.model.js";
import ParametersType from "../models/parametersType.model.js";
import Parameters from "../models/parameters.model.js";
import ParametersParameterType from "../models/parametersParameterType.model.js";
import Systems from "../models/systems.model.js";
import ParameterCategories from "../models/parameterCategories.model.js";
import User from "../models/user.model.js";




const saveConfiguration = async (req ,res) =>{
    try {
        const user = req.user
        let configurations = await GlobalConfiguration.findOne({
            order: [['created_at', 'DESC']],
            where: { tenant_id: user.tenant_id },
          });

          const globalConfig = req.body.systems

          for (const systemKey in globalConfig) {
            if (globalConfig.hasOwnProperty(systemKey)) {
              const system = globalConfig[systemKey];
        
              if (!system.systemId) {

                // Check if the system exists by name using Sequelize's findAll method
                const systemRecord = await Systems.findOne({
                    where: { name: systemKey ,tenant_id: user.tenant_id}, 
                    attributes: ["id", "name"],
                });
        
                if (systemRecord) {
                  system.systemId = systemRecord.id;

                } else {
                  const newSystem = await Systems.create({ name: systemKey ,tenant_id: user.tenant_id});
                  system.systemId = newSystem.id;
                }
              }

              const checkIfTableExists = async (tableName) => {
                try {
                  const queryInterface = Systems.sequelize.getQueryInterface();
                  const tables = await queryInterface.showAllTables();
              
                  // Check if the table name exists in the list of tables
                  return tables.includes(tableName);
                } catch (error) {
                  console.error("Error checking table existence:", error);
                  return false;
                }
              };

              if (system.DataSource && typeof system.DataSource === "object") {
                const invalidDataSources = [];
      
                for (const [sourceKey, identifierArray] of Object.entries(system.DataSource)) {
                  // Validate value is an array of strings
                  if (!Array.isArray(identifierArray) || !identifierArray.every((val) => typeof val === "string")) {
                    return generateResponse(
                      res,
                      400,
                      `DataSource for '${sourceKey}' must be an array of strings`
                    );
                  }
                
                  for (const identifier of identifierArray) {
                    const tableExists = await checkIfTableExists(identifier);
                    if (!tableExists) {
                      invalidDataSources.push(identifier);
                    }
                  }
                }
      
                if (invalidDataSources.length > 0) {
                  return generateResponse(
                    res,
                    400,
                    `The following DataSources are invalid (tables do not exist): ${invalidDataSources.join(", ")}`
                  );
                }
              }
            }
          }

          if (configurations) {
            // Update the existing record
            configurations = await configurations.update({
              configurations: req.body.systems,
            });
          } else {
            // Create a new record
            configurations = await GlobalConfiguration.create({
              tenant_id: user.tenant_id,
              configurations: req.body.systems,
            });
          }
          
        return generateResponse(res, 200, 'Configurations is save successfully',configurations)
    } catch (error) {
    console.log('error :', error);
    return generateResponse(res, 500, 'Failed to save Configurations')
    }
}

const materials = async (req, res) => {

    try {
        const materials = await Materials.findAll({
            attributes:['id','name','code']
        })
        return generateResponse(res, 200, 'Materials list data fetched successfully.',materials)
    } catch (error) {
        console.error('error :', error);
        return generateResponse(res, 500, 'Failed to upload file.')
    }
}

const qualityParameter = async (req, res) => {

    try {

        const qualityParameter = await qualityParameters.findAll({
            attributes:['id','name','code']
        })
        return generateResponse(res, 200, 'Quality Parameters list fetched successfully.' ,qualityParameter)
    } catch (error) {
        console.error('error :', error);
        return generateResponse(res, 500, 'Failed to Fetch Quality Parameters Data.')
    }
}

const process = async (req, res) => {

    try {
        const processesData = await processes.findAll({
            attributes:["id","name","code"]
          });

        return generateResponse(res, 200, 'processes list fetched successfully.',processesData)
    } catch (error) {
        console.error('error :', error);
        return generateResponse(res, 500, 'Failed to upload file.')
    }
}


const parameter = async (req, res) => {

    try {
        const parametersData = await parameters.findAll({
            attributes:["id","name"]
          });

        return generateResponse(res, 200, 'parameters list fetched successfully.',parametersData)
    } catch (error) {
        console.error('error :', error);
        return generateResponse(res, 500, 'Failed to upload file.')
    }
}

const parametersTypes = async (req, res) => {

    try {
        const parametersData = await ParametersType.findAll({
            attributes:["id","name"]
          });

        return generateResponse(res, 200, 'ParametersType list fetched successfully.',parametersData)
    } catch (error) {
        console.error('error :', error);
        return generateResponse(res, 500, 'Failed to upload file.')
    }
}

const systems = async (req, res) => {

    try {
        const systems = await Systems.findAll({
            attributes:["id","name"]
          });

        return generateResponse(res, 200, 'Systems data fetched successfully.',systems)
    } catch (error) {
        console.error('error :', error);
        return generateResponse(res, 500, 'Failed to upload file.')
    }
}

const parameterCategories = async (req, res) => {

    try {
        const parameterCategorie = await ParameterCategories.findAll({
            attributes:["id","name"]
          });

        return generateResponse(res, 200, 'Systems data fetched successfully.',parameterCategorie)
    } catch (error) {
        console.error('error :', error);
        return generateResponse(res, 500, 'Failed to upload file.')
    }
}

const getConfiguration = async (req, res) => {
    try {
        const { tenant_id } = req.user;
        
        // Get the latest configuration for this tenant
        const configuration = await GlobalConfiguration.findOne({
            where: { tenant_id },
            order: [['created_at', 'DESC']], // Get the most recent configuration
            attributes: ['configurations', 'created_at']
        });

        if (!configuration) {
            return generateResponse(res, 200, 'No configuration found', {});
        }

        const updatedConfigurations = { ...configuration.configurations };
        const updatedConfigsTemp = {}; 
    
        
        // Iterate through the configurations to check if systemId exists and modify accordingly
        for (const systemKey in updatedConfigurations) {
          if (updatedConfigurations.hasOwnProperty(systemKey)) {
            const system = updatedConfigurations[systemKey];
        
            // If systemId exists, find the system name from the database
            if (system.systemId) {
              let systemNameFromDb = await Systems.findOne({
                where: { id: system.systemId },
                attributes: ['name']
              });
        
              if (systemNameFromDb) {
                systemNameFromDb = systemNameFromDb.dataValues.name;
                updatedConfigsTemp[systemNameFromDb] = system
              } else {
                // No system name found for systemId
                updatedConfigsTemp[systemKey] = system
              }
            }else{
                updatedConfigsTemp[systemKey] = system
            }
          }
        }

        const selectedSystems = await User.findOne({
          where: { id: req.user.id },
          attributes: ['selected_systems']
        });
        
        // return generateResponse(res, 200, 'Configuration fetched successfully', updatedConfigsTemp);

        return res.status(200).json({
          message: 'Configuration fetched successfully',
          data: updatedConfigsTemp,
          selectedSystems: selectedSystems.selected_systems
      });

    } catch (error) {
        console.error('error:', error);
        return generateResponse(res, 500, 'Failed to fetch configuration');
    }
};

export {
    saveConfiguration,
    materials,
    qualityParameter,
    process,
    parameter,
    parametersTypes,
    systems,
    parameterCategories,
    getConfiguration
}
