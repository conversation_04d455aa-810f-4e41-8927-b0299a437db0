import { DataTypes, Model } from 'sequelize';
import { sequelize } from '../database/dbConnection.js'; // Import the Sequelize instance

class ParameterCategories extends Model {}

ParameterCategories.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false,  // Ensuring that the name is required
    },
    code: {
      type: DataTypes.STRING(255),
      allowNull: false,  // Ensuring that the code is required
      unique: true,  // Optionally, enforce that 'code' is unique
    },
  },
  {
    sequelize, 
    modelName: 'ParameterCategories',
    tableName: 'parameter_categories',  // Table name in the database
    timestamps: true,  // Enable timestamps for createdAt and updatedAt
    underscored: true, // Use snake_case for column names in the database
  }
);

export default ParameterCategories;
