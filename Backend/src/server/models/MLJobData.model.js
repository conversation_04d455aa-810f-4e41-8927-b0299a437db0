import mongoose from "mongoose";
const { Schema } = mongoose;

const MLJobDataSchema = new Schema({
    workflow_id: {
        type: Number,
        required: true,
    },
    ml_job_id: {
        type: Schema.Types.ObjectId, // Assuming it's an ObjectId reference to M<PERSON>Job
        ref: 'MLJob',
        required: true,
      },
    result: {
        type: Schema.Types.Mixed, // JSONB equivalent in Mongoose
        required: true
    }
}, {
    timestamps: { createdAt: 'created_at', updatedAt: false },
    collection: 'ml_jobs_data'
}); // Enable only createdAt timestamp

// Export the model
const MLJobData = mongoose.model('MLJobData', MLJobDataSchema);

export default MLJobData
