// const { DataTypes } = require('sequelize');
import { DataTypes } from "sequelize";
import { sequelize } from "../database/dbConnection.js";
import User from "./user.model.js";

const CausalInference = sequelize.define('causal_inference', {
    id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
    },
    workflow_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
            model: 'workflows',
            key: 'id'
        },
        onDelete: 'CASCADE'
    },
    run_id: {
        type: DataTypes.STRING,
        allowNull: true,
    },
    task_id: {
        type: DataTypes.STRING,
        allowNull: true,
    },
    target_col: {
        type: DataTypes.STRING,
        allowNull: true,
    },
    outlier: {
        type: DataTypes.JSON,
        allowNull: true,
    },
    inference: {
        type: DataTypes.JSON,
        allowNull: true,
    },
    user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
    },
    tenant_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
    },
    status: {
        type: String,
        enum: ['done', 'in_progress', 'failed'],
        required: true,
        default: 'in_progress',
    },
    s3_model_path: {
        type: DataTypes.STRING,
        allowNull: true,
    },
    created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
    },
    updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
    },
}, {
    tableName: 'causal_inference',
    timestamps: false,
});

CausalInference.belongsTo(User, { foreignKey: 'user_id', as: 'creator' });

export default CausalInference;
