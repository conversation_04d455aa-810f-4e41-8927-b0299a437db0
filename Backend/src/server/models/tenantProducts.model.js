import { DataTypes, Model } from 'sequelize';
import { sequelize } from '../database/dbConnection.js'; // Import the Sequelize instance
import Tenants from './tenants.model.js';  // Import the Tenants model
import Products from './products.model.js';  // Import the Products model

class TenantProducts extends Model {}

TenantProducts.init(
  {
    tenant_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: Tenants, // Reference to the Tenants model
        key: 'id',
      },
      onDelete: 'CASCADE', // Ensures that deleting a tenant deletes its associated products
    },
    product_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: Products, // Reference to the Products model
        key: 'id',
      },
      onDelete: 'CASCADE', // Ensures that deleting a product deletes its associated tenants
    },
  },
  {
    sequelize,
    modelName: 'TenantProducts',
    tableName: 'tenant_products',
    timestamps: false,  // This table may not need timestamps (createdAt, updatedAt)
    underscored: true, // Use snake_case for column names in the database
  }
);

// Setting up the associations (Optional, if you want to access related models)
Tenants.belongsToMany(Products, {
  through: TenantProducts,
  foreignKey: 'tenant_id',
});

Products.belongsToMany(Tenants, {
  through: TenantProducts,
  foreignKey: 'product_id',
});

export default TenantProducts;
