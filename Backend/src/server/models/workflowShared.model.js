// const { DataTypes } = require('sequelize');
import { DataTypes } from "sequelize";
import { sequelize } from "../database/dbConnection.js";
import WorkflowStructure from "./workflowStructure.model.js";
import WorkflowComponents from "./workflowComponents.model.js";
import Folder from "./folder.model.js";
import workflowFilters from "./workflowFilters.model.js";

const WorkflowShared = sequelize.define('workflow_shared', {
    id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
    },
    user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
          model: 'Users',
          key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
    },
    share_to: {
        type: DataTypes.ENUM('tenant', 'users'),
        allowNull: false,
    },
    shared_to_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
    },
    access_level: {
        type: DataTypes.ENUM('edit', 'execute', 'view'),
        allowNull: false,
    },
    share_type: {
        type: DataTypes.ENUM('workflow', 'runs'),
        allowNull: false,
    },
    share_type_id: {
        type: DataTypes.STRING,
        allowNull: false,
    },
    created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
    },
    updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
    },
}, {
    tableName: 'workflow_shared',
    timestamps: false,
});

export default WorkflowShared;
