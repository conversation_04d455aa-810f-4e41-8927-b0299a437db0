// const { DataTypes } = require('sequelize');
import { DataTypes } from "sequelize";
import { sequelize } from "../database/dbConnection.js";
import Comments from "./comments.model.js";
import User from "./user.model.js";

const Tasks = sequelize.define('tasks', {
    id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
    },
    tenant_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
    },
    assign_to: {
        type: DataTypes.INTEGER,
        allowNull: false,
    },
    user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
    },
    status: {
        type: String,
        enum: ['todo', 'in_progress', 'completed', 'not_verified'],
        required: true,
        default: 'todo',
    },
    generic_identifier: {
        type: DataTypes.JSON,
        allowNull: false,
    },
    header_value: {
        type: DataTypes.STRING,
        allowNull: false,
    },
    comment: {
        type: DataTypes.STRING,
        allowNull: false,
    },
    completed_comment: {
        type: DataTypes.STRING,
        allowNull: true,
    },
    cell_value: {
        type: DataTypes.STRING,
        allowNull: false,
      },
    date_time: {
        type: DataTypes.STRING,
        allowNull: false,
    },
    run_deviation_data: {
        type: DataTypes.JSONB,
        allowNull: true,
    },
    created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
    },
    updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
    },
}, {
    tableName: 'tasks',
    timestamps: false,
});

Tasks.belongsTo(User, { foreignKey: 'user_id', as: 'creator' });
Tasks.belongsTo(User, { foreignKey: 'assign_to', as: 'assignee' });

Comments.belongsTo(Tasks, { foreignKey: 'task_id' });
Comments.belongsTo(User, { foreignKey: 'user_id' });

Tasks.hasMany(Comments, { foreignKey: 'task_id' });
User.hasMany(Comments, { foreignKey: 'user_id' });

export default Tasks;
