import { DataTypes, Model } from 'sequelize';
import { sequelize } from '../database/dbConnection.js'; // Import the Sequelize instance
import Materials from './materials.model.js';  // Import the Materials model
import qualityParameters from './qualityParameters.model.js';
class MaterialQualityParameters extends Model {}

MaterialQualityParameters.init(
  {
    material_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: Materials, // Reference to the Materials model
        key: 'id',
      },
      onDelete: 'CASCADE', // Ensures that deleting a material deletes its associated quality parameters
    },
    quality_parameter_id: {
      type: DataTypes.INTEGER,
      allowNull: false,
      references: {
        model: qualityParameters, // Reference to the QualityParameters model
        key: 'id',
      },
      onDelete: 'CASCADE', // Ensures that deleting a quality parameter deletes its associated materials
    },
  },
  {
    sequelize,
    modelName: 'MaterialQualityParameters',
    tableName: 'material_quality_parameters',
    timestamps: false,  // This table may not need timestamps (createdAt, updatedAt)
    underscored: true, // Use snake_case for column names in the database
  }
);

// Setting up the associations (Optional, if you want to access related models)
Materials.belongsToMany(qualityParameters, {
  through: MaterialQualityParameters,
  foreignKey: 'material_id',
});

qualityParameters.belongsToMany(Materials, {
  through: MaterialQualityParameters,
  foreignKey: 'quality_parameter_id',
});


export default MaterialQualityParameters;