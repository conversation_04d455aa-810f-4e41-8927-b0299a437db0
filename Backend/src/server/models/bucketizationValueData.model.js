// const { DataTypes } = require('sequelize');
import { DataTypes } from "sequelize";
import { sequelize } from "../database/dbConnection.js";
import workflowFilters from "./workflowFilters.model.js";
const bucketizationlValueData = sequelize.define('bucketization_value_data', {
    id: {
        type: DataTypes.INTEGER,
        primaryKey: true,
        autoIncrement: true,
    },
    user_id: {
        type: DataTypes.INTEGER,
        allowNull: false,
        references: {
            model: 'Users',
            key: 'id'
        }
    },
    name: {
        type: DataTypes.STRING,
        allowNull: false,
    },
    workflow_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
        references: {
            model: 'workflows',
            key: 'id'
        },
        onDelete: 'CASCADE'
    },
    filter_id: {
        type: DataTypes.INTEGER,
        allowNull: true,
    },
    bucketization_statistical_value: {
        type: DataTypes.JSON,
        allowNull: false,
    },
    input_material_type: {
        type: DataTypes.ARRAY(DataTypes.STRING),  // Array of strings
        allowNull: true,
    },
    created_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
    },
    updated_at: {
        type: DataTypes.DATE,
        defaultValue: DataTypes.NOW,
    },
}, {
    tableName: 'bucketization_value_data',
    timestamps: false,
});

bucketizationlValueData.belongsTo(workflowFilters, { 
    foreignKey: 'filter_id',
    as: 'workflowFilter'
  });

export default bucketizationlValueData;
