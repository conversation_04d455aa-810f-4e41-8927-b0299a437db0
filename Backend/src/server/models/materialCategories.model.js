import { DataTypes, Model } from 'sequelize';
import { sequelize } from '../database/dbConnection.js'; // Import the Sequelize instance

class MaterialCategories extends Model {}

MaterialCategories.init(
  {
    id: {
      type: DataTypes.INTEGER,
      autoIncrement: true,
      primaryKey: true,
      allowNull: false,
    },
    name: {
      type: DataTypes.STRING(255),
      allowNull: false,  // Ensuring the name field is required
    },
    code: {
      type: DataTypes.STRING(255),
      allowNull: false,  // Ensuring the code field is required
      unique: true,  // Optionally, enforce that 'code' is unique
    },
  },
  {
    sequelize, 
    modelName: 'MaterialCategories',
    tableName: 'material_categories',  // The table name in the database
    timestamps: true,  // Enable timestamps for createdAt and updatedAt
    underscored: true, // Use snake_case for column names in the database
  }
);

export default MaterialCategories;
