const express = require('express');
const path = require('path');

const app = express();

// Serve the static files from the 'public' folder
app.use(express.static(path.join(__dirname, 'public')));

// Serve the React app on any unmatched route
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, 'public/frontend', 'index.html'));
});

const PORT = process.env.PORT || 5001;
app.listen(PORT, () => {
  console.log(`Server is running on port ${PORT}`);
});
