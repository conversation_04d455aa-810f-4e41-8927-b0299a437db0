import dayjs from 'dayjs';
// Process data to format date and convert values to numbers
  export const processData =(data:any) =>{
    
      data.map((item:any, index:number) => ({
        key: index,
        date: dayjs(item.Time).format('MM/DD/YYYY'),
        ...Object.fromEntries(
          Object.entries(item).map(([key, value]) => (key !== 'Time' ? [key.trim(), parseFloat(value as string)] : [key, value]))
        ),
      }));
  } 