import { useState } from "react";

const StatusUpdateModal: React.FC<{
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (comment: string) => void;
}> = ({ isOpen, onClose, onSubmit }) => {
  const [comment, setComment] = useState('');

  const handleSubmit = () => {
    onSubmit(comment);
    setComment('');
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center">
      <div className="bg-white p-6 rounded-lg shadow-lg w-1/3">
        <h3 className="text-xl font-semibold mb-4">Add Comment</h3>
        <textarea
          className="w-full p-2 border rounded mb-4"
          placeholder="Enter comment here"
          value={comment}
          onChange={(e) => setComment(e.target.value)}
        />
        <div className="flex justify-end">
          <button
            className="bg-primary text-white px-4 py-2 rounded mr-2"
            onClick={handleSubmit}
          >
            Update
          </button>
          <button
            className="bg-gray-300 px-4 py-2 rounded"
            onClick={onClose}
          >
            Cancel
          </button>
        </div>
      </div>
    </div>
  );
};

export default StatusUpdateModal; 