import React, { useState } from 'react';

interface SaveModalProps {
  isOpen: boolean;
  type: string;
  onClose: () => void;
  onSave: (workflowName: string,type:string) => void;
}

export const SaveModal: React.FC<SaveModalProps> = ({ isOpen, onClose, onSave ,type}) => {
  const [workflowName, setWorkflowName] = useState<string>('');
  const [workflowErr, setWorkflowErr] = useState<string>('')

  const handleSaveClick = () => {
    if (workflowName.trim()) {
      onSave(workflowName,type);
      setWorkflowName('');
      onClose(); 
    } else {
      setWorkflowErr('Enter Workflow Name')
    }
  };
  const onCloseModal = () =>{
      onClose()
      setWorkflowErr('')
      setWorkflowName('');
  }

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center items-center z-50">
      <div className="bg-white p-6 rounded-lg w-80 shadow-lg">
        <h2 className="text-lg font-semibold text-center mb-4">Enter Workflow Name</h2>
        <input
          type="text"
          value={workflowName}
          onChange={(e) => setWorkflowName(e.target.value)}
          className="w-full px-4 py-2 border border-gray-300 rounded-md"
          placeholder="Enter workflow name"
        />

        <div className="text-red-700">
        {
        !workflowName && 
            <span>
            {workflowErr}
            </span>
        }
        </div>
        <div className="flex justify-between mt-5">
          <button
          id="saveButton"
            onClick={onCloseModal}
            className=" save-section px-4 py-2 bg-gray-300 rounded-md text-sm font-semibold hover:bg-gray-400"
          >
            <span className="save-section">
            Cancel
            </span>
        
          </button>
          <button
            onClick={handleSaveClick}
            className="save-section btn-primary-new"
          >
        <span className="save-section">
          Save
        </span>
          </button>
        </div>
      </div>
    </div>
  );
};

