import React, { useEffect, useRef, useState } from 'react';

interface SaveModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (goldenBatchName: string) => void;
}

export const SaveGoldenBatchModal: React.FC<SaveModalProps> = ({ isOpen, onClose, onSave }) => {
  const [goldenBatchName, setgoldenBatchName] = useState<string>('');
  const [goldenBatchErr, setgoldenBatchErr] = useState<string>('')
  const inputRef = useRef<HTMLInputElement>(null);
  
  const handleSaveClick = () => {
    if (goldenBatchName.trim()) {
      onSave(goldenBatchName);
      setgoldenBatchName('');
      onClose(); 
    } else {
        setgoldenBatchErr('Enter Golden Batch Name')
    }
  };
  const onCloseModal = () =>{
      onClose()
      setgoldenBatchErr('')
      setgoldenBatchName('');
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleSaveClick();
    }
  };

  useEffect(() => {
    if (isOpen && inputRef?.current) {
      inputRef.current.focus();
    }
  }, [isOpen]);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex justify-center z-50">
      <div className="bg-white p-6 m-32 rounded-lg w-80 shadow-lg h-48">
        <h2 className="text-lg font-semibold text-center mb-4">Enter Golden Batch Name</h2>
        <input
          type="text"
          ref={inputRef}
          value={goldenBatchName}
          onChange={(e) => setgoldenBatchName(e.target.value)}
          onKeyDown={handleKeyDown}
          className="w-full px-4 py-2 border border-gray-300 rounded-md"
          placeholder="Enter Golden Batch Name"
        />

        <div className="text-red-700">
        {
        !goldenBatchName && 
            <span>
            {goldenBatchErr}
            </span>
        }
        </div>
        <div className="flex justify-between mt-5">
          <button
            onClick={onCloseModal}
            className=" save-section px-4 py-2 bg-gray-300 rounded-md text-sm font-semibold hover:bg-gray-400"
          >
            <span className="save-section">
            Cancel
            </span>
        
          </button>
          <button
            onClick={handleSaveClick}
            className="save-section !h-9 w-16 btn-primary-new"
          >
        <span className="save-section">
          Save
        </span>
          </button>
        </div>
      </div>
    </div>
  );
};

