"use client"

import { animate } from "plotly.js";
import { use<PERSON><PERSON>back, useState } from "react"
import ReactFlow, {
  Controls,
  Background,
  BackgroundVariant,
  MiniMap,
  useNodesState,
  useEdgesState,
  addEdge,
  type Connection,
  MarkerType,
  Handle,
  Node
} from "reactflow"
import "reactflow/dist/style.css"


// Sample JSON data (can be replaced with actual data)
// const sampleData = {
//   "nodes": [
//     { "id": "recipeNo", "label": "Recipe_No", "x": 100, "y": 100 },
//     { "id": "fillingStopTime", "label": "Filling_Stop_Time", "x": 200, "y": 150 },
//     { "id": "fillingRPM", "label": "Filling_RPM", "x": 300, "y": 150 },
//     { "id": "milkQuantityInVat", "label": "Milk_Quantity_In_Vat", "x": 400, "y": 150 },
//     { "id": "temperatureOfMilkInVat", "label": "Temperature_Of_Milk_In_Vat", "x": 500, "y": 150 },
//     { "id": "cultureQuantity", "label": "Culture_Quantity", "x": 600, "y": 150 },
//     { "id": "cultureAdditionTime", "label": "Culture_Addition_Time", "x": 700, "y": 150 },
//     { "id": "rennetQuantity", "label": "Rennet_Quantity", "x": 800, "y": 150 },
//     { "id": "rennetAdditionTime", "label": "Rennet_Addition_Time", "x": 900, "y": 150 },
//     { "id": "coagulationStartTime", "label": "Coagulation_Start_Time", "x": 100, "y": 250 },
//     { "id": "coagulationStopTime", "label": "Coagulation_Stop_Time", "x": 200, "y": 250 },
//     { "id": "cuttingStartTime", "label": "Cutting_Start_Time", "x": 300, "y": 250 },
//     { "id": "cuttingRPM", "label": "Cutting_RPM", "x": 400, "y": 250 },
//     { "id": "intermittentCuttingStartTime", "label": "Intermittent_Cutting_Start_Time", "x": 500, "y": 250 },
//     { "id": "intermittentCuttingRPM", "label": "Intermittent_Cutting_RPM", "x": 600, "y": 250 },
//     { "id": "cookingStartTime", "label": "Cooking_Start_Time", "x": 700, "y": 250 },
//     { "id": "cookingStopTime", "label": "Cooking_Stop_Time", "x": 800, "y": 250 },
//     { "id": "cookingRPM", "label": "Cooking_RPM", "x": 900, "y": 250 },
//     { "id": "cookingTemperature", "label": "Cooking_Temperature", "x": 100, "y": 350 },
//     { "id": "emptyingStartTime", "label": "Emptying_Start_Time", "x": 200, "y": 350 },
//     { "id": "emptyingStopTime", "label": "Emptying_Stop_Time", "x": 300, "y": 350 },
//     { "id": "residenceTimeInVat", "label": "Residence_Time_In_Vat", "x": 400, "y": 350 },
//     { "id": "totalProcesstimeInVat", "label": "Total_Process_Time_In_Vat", "x": 500, "y": 350 },
//     { "id": "vatpH", "label": "Vat_pH", "x": 600, "y": 350 }
//   ],
//   "edges": [
//     { "source": "recipeNo", "target": "fillingStopTime", "value": 0.29 },
//     { "source": "recipeNo", "target": "fillingRPM", "value": -0.12 },
//     { "source": "recipeNo", "target": "milkQuantityInVat", "value": 0.55 },
//     { "source": "recipeNo", "target": "temperatureOfMilkInVat", "value": -0.74 },
//     { "source": "recipeNo", "target": "cultureQuantity", "value": 0.88 },
//     { "source": "recipeNo", "target": "cultureAdditionTime", "value": -0.41 },
//     { "source": "recipeNo", "target": "rennetQuantity", "value": 0.17 },
//     { "source": "recipeNo", "target": "rennetAdditionTime", "value": -0.62 },
//     { "source": "recipeNo", "target": "coagulationStartTime", "value": 0.96 },
//     { "source": "recipeNo", "target": "coagulationStopTime", "value": -0.53 },
//     { "source": "recipeNo", "target": "cuttingStartTime", "value": 0.42 },
//     { "source": "recipeNo", "target": "cuttingRPM", "value": 0.73 },
//     { "source": "recipeNo", "target": "intermittentCuttingStartTime", "value": -0.25 },
//     { "source": "recipeNo", "target": "intermittentCuttingRPM", "value": -0.35 },
//     { "source": "recipeNo", "target": "cookingStartTime", "value": 0.68 },
//     { "source": "recipeNo", "target": "cookingStopTime", "value": -0.14 },
//     { "source": "recipeNo", "target": "cookingRPM", "value": 0.59 },
//     { "source": "recipeNo", "target": "cookingTemperature", "value": -0.22 },
//     { "source": "recipeNo", "target": "emptyingStartTime", "value": 0.93 },
//     { "source": "recipeNo", "target": "emptyingStopTime", "value": -0.17 },
//     { "source": "recipeNo", "target": "residenceTimeInVat", "value": 0.49 },
//     { "source": "recipeNo", "target": "totalProcesstimeInVat", "value": -0.36 },
//     { "source": "fillingStopTime", "target": "vatpH", "value": 0.82 },
//     { "source": "fillingRPM", "target": "vatpH", "value": -0.39 },
//     { "source": "milkQuantityInVat", "target": "vatpH", "value": 0.26 },
//     { "source": "temperatureOfMilkInVat", "target": "vatpH", "value": -0.78 },
//     { "source": "cultureQuantity", "target": "vatpH", "value": 0.61 },
//     { "source": "cultureAdditionTime", "target": "vatpH", "value": -0.48 },
//     { "source": "rennetQuantity", "target": "vatpH", "value": 0.14 },
//     { "source": "rennetAdditionTime", "target": "vatpH", "value": -0.25 },
//     { "source": "coagulationStartTime", "target": "vatpH", "value": 0.73 },
//     { "source": "coagulationStopTime", "target": "vatpH", "value": -0.89 },
//     { "source": "cuttingStartTime", "target": "vatpH", "value": 0.56 },
//     { "source": "cuttingRPM", "target": "vatpH", "value": -0.22 },
//     { "source": "intermittentCuttingStartTime", "target": "vatpH", "value": 0.95 },
//     { "source": "intermittentCuttingRPM", "target": "vatpH", "value": -0.11 },
//     { "source": "cookingStartTime", "target": "vatpH", "value": 0.33 },
//     { "source": "cookingStopTime", "target": "vatpH", "value": -0.47 },
//     { "source": "cookingRPM", "target": "vatpH", "value": 0.64 },
//     { "source": "cookingTemperature", "target": "vatpH", "value": -0.82 },
//     { "source": "emptyingStartTime", "target": "vatpH", "value": 0.79 },
//     { "source": "emptyingStopTime", "target": "vatpH", "value": -0.59 },
//     { "source": "residenceTimeInVat", "target": "vatpH", "value": 0.25 },
//     { "source": "totalProcesstimeInVat", "target": "vatpH", "value": -0.06 },
//     { "source": "cookingStartTime", "target": "cookingStopTime", "value": 0.68 },
//     { "source": "emptyingStartTime", "target": "emptyingStopTime", "value": -0.11 }
//   ]
// };

const sampleData = {
  "nodes": [
    { "id": "recipeNo", "label": "Recipe No", "x": 100, "y": 550 },
    { "id": "fillingStopTime", "label": "Filling Stop Time", "x": 500, "y": 150 },
    { "id": "fillingRPM", "label": "Filling RPM", "x": 500, "y": 200 },
    { "id": "milkQuantityInVat", "label": "Milk Quantity In Vat", "x": 500, "y": 250 },
    { "id": "temperatureOfMilkInVat", "label": "Temperature Of Milk In Vat", "x": 500, "y": 300 },
    { "id": "cultureQuantity", "label": "Culture Quantity", "x": 500, "y": 350 },
    { "id": "cultureAdditionTime", "label": "Culture Addition Time", "x": 500, "y": 400 },
    { "id": "rennetQuantity", "label": "Rennet Quantity", "x": 500, "y": 450 },
    { "id": "rennetAdditionTime", "label": "Rennet Addition Time", "x": 500, "y": 500 },
    { "id": "coagulationStartTime", "label": "Coagulation Start Time", "x": 500, "y": 550 },
    { "id": "coagulationStopTime", "label": "Coagulation Stop Time", "x": 500, "y": 600 },
    { "id": "cuttingStartTime", "label": "Cutting Start Time", "x": 500, "y": 650 },
    { "id": "cuttingRPM", "label": "Cutting RPM", "x": 500, "y": 700 },
    { "id": "intermittentCuttingStartTime", "label": "Intermittent Cutting Start Time", "x": 500, "y": 750 },
    { "id": "intermittentCuttingRPM", "label": "Intermittent Cutting RPM", "x": 500, "y": 800 },
    { "id": "cookingStartTime", "label": "Cooking Start Time", "x": 500, "y": 850 },
    { "id": "cookingStopTime", "label": "Cooking Stop Time", "x": 500, "y": 900 },
    { "id": "cookingRPM", "label": "Cooking RPM", "x": 500, "y": 950 },
    { "id": "cookingTemperature", "label": "Cooking Temperature", "x": 500, "y": 1000 },
    { "id": "emptyingStartTime", "label": "Emptying Start Time", "x": 500, "y": 1050 },
    { "id": "emptyingStopTime", "label": "Emptying Stop Time", "x": 500, "y": 1100 },
    { "id": "residenceTimeInVat", "label": "Residence Time In Vat", "x": 500, "y": 1150 },
    { "id": "totalProcesstimeInVat", "label": "Total Process Time In Vat", "x": 500, "y": 1200 },
    { "id": "vatpH", "label": "Vat pH", "x": 900, "y": 550 }
  ],
  "edges": [
    { "source": "recipeNo", "target": "fillingStopTime", "value": 0.32 },
    { "source": "recipeNo", "target": "fillingRPM", "value": 0.12 },
    { "source": "recipeNo", "target": "milkQuantityInVat", "value": 0.27 },
    { "source": "recipeNo", "target": "temperatureOfMilkInVat", "value": 0.45 },
    { "source": "recipeNo", "target": "cultureQuantity", "value": -0.12 },
    { "source": "recipeNo", "target": "cultureAdditionTime", "value": 0.15 },
    { "source": "recipeNo", "target": "rennetQuantity", "value": 0.23 },
    { "source": "recipeNo", "target": "rennetAdditionTime", "value": -0.18 },
    { "source": "recipeNo", "target": "coagulationStartTime", "value": 0.28 },
    { "source": "recipeNo", "target": "coagulationStopTime", "value": -0.04 },
    { "source": "recipeNo", "target": "cuttingStartTime", "value": 0.22 },
    { "source": "recipeNo", "target": "cuttingRPM", "value": 0.19 },
    { "source": "recipeNo", "target": "intermittentCuttingStartTime", "value": -0.21 },
    { "source": "recipeNo", "target": "intermittentCuttingRPM", "value": 0.11 },
    { "source": "recipeNo", "target": "cookingStartTime", "value": -0.13 },
    { "source": "recipeNo", "target": "cookingStopTime", "value": 0.38 },
    { "source": "recipeNo", "target": "cookingRPM", "value": 0.27 },
    { "source": "recipeNo", "target": "cookingTemperature", "value": -0.22 },
    { "source": "recipeNo", "target": "emptyingStartTime", "value": 0.24 },
    { "source": "recipeNo", "target": "emptyingStopTime", "value": -0.16 },
    { "source": "recipeNo", "target": "residenceTimeInVat", "value": 0.06 },
    { "source": "recipeNo", "target": "totalProcesstimeInVat", "value": -0.03 },
    { "source": "fillingStopTime", "target": "vatpH", "value": 0.15 },
    { "source": "fillingRPM", "target": "vatpH", "value": -0.07 },
    { "source": "milkQuantityInVat", "target": "vatpH", "value": 0.25 },
    { "source": "temperatureOfMilkInVat", "target": "vatpH", "value": 0.43 },
    { "source": "cultureQuantity", "target": "vatpH", "value": -0.13 },
    { "source": "cultureAdditionTime", "target": "vatpH", "value": 0.18 },
    { "source": "rennetQuantity", "target": "vatpH", "value": -0.05 },
    { "source": "rennetAdditionTime", "target": "vatpH", "value": 0.12 },
    { "source": "coagulationStartTime", "target": "vatpH", "value": -0.09 },
    { "source": "coagulationStopTime", "target": "vatpH", "value": 0.34 },
    { "source": "cuttingStartTime", "target": "vatpH", "value": 0.08 },
    { "source": "cuttingRPM", "target": "vatpH", "value": 0.24 },
    { "source": "intermittentCuttingStartTime", "target": "vatpH", "value": 0.22 },
    { "source": "intermittentCuttingRPM", "target": "vatpH", "value": -0.14 },
    { "source": "cookingStartTime", "target": "vatpH", "value": 0.35 },
    { "source": "cookingStopTime", "target": "vatpH", "value": -0.21 },
    { "source": "cookingRPM", "target": "vatpH", "value": 0.18 },
    { "source": "cookingTemperature", "target": "vatpH", "value": 0.28 },
    { "source": "emptyingStartTime", "target": "vatpH", "value": -0.19 },
    { "source": "emptyingStopTime", "target": "vatpH", "value": 0.23 },
    { "source": "residenceTimeInVat", "target": "vatpH", "value": 0.11 },
    { "source": "totalProcesstimeInVat", "target": "vatpH", "value": -0.16 }
  ]
};

// const sampleData = {
//   nodes: [
//     { id: "cooking_ph", label: "Cooking_pH", x: 100, y: 100 },
//     { id: "temperature_milk", label: "Temperature_of_Milk_in_vat", x: 500, y: 100 },
//     { id: "vat_ph", label: "VatpH", x: 100, y: 300 },
//     { id: "culture_ph", label: "Culture_pH", x: 500, y: 300 },
//   ],
//   edges: [
//     { source: "cooking_ph", target: "temperature_milk", value: 0.23 },
//     { source: "cooking_ph", target: "vat_ph", value: 0.01 },
//     { source: "cooking_ph", target: "culture_ph", value: -0.24 },
//     { source: "vat_ph", target: "culture_ph", value: -0.05 },
//     { source: "temperature_milk", target: "culture_ph", value: 0.03 },
//   ],
// }

export default function DirectedGraph({ data = sampleData }) {
  // Convert input JSON to React Flow format
  const parseGraphData = useCallback((inputData: any) => {
    const nodes = inputData.nodes.map((node: any) => ({
      id: node.id,
      data: { label: node.label },
      position: { x: node.x, y: node.y },
      style: {
        border: "1px solid #777",
        padding: "10px",
        borderRadius: "15px",
        background: "white",
        fontSize: "12px",
      },
      sourcePosition: "right", // Add handle on the right
      targetPosition: "left", // Add handle on the left
    }))

    const edges = inputData.edges.map((edge: any, index: any) => {
      // Determine color based on value
      const edgeColor = edge.value >= 0 ? "#7ac555" : "#ff0000" // Green for positive, Red for negative
      const edgeType = edge.value < 0 ? "dashed" : "solid"
      // Calculate edge width based on edge value.
      const edgeWidth = 10 * edge.value

      return {
        id: `e${index}`,
        source: edge.source,
        target: edge.target,
        label: edge.value.toString(),
        animated: true,
        style: {
          stroke: edgeColor,
          strokeWidth: edgeWidth,
          // strokeDasharray: ""
          strokeDasharray: edgeType === "dashed" ? "5,5" : "10,5",
        },
        labelBgPadding: [4, 2],
        labelBgBorderRadius: 4,
        markerEnd: {
          type: MarkerType.ArrowClosed,
          color: edgeColor,
        },
        arrowHeadType: "arrowclosed",
      }
    })

    return { nodes, edges }
  }, [])

  // Parse and set initial nodes & edges
  const { nodes: initialNodes, edges: initialEdges } = parseGraphData(data)
  const [nodes, setNodes, onNodesChange] = useNodesState(initialNodes)
  const [edges, setEdges, onEdgesChange] = useEdgesState(initialEdges)

  // Handle new connections
  const onConnect = useCallback(
    (connection: Connection) => {
      setEdges((eds) =>
        addEdge(
          {
            ...connection,
            markerEnd: { type: MarkerType.ArrowClosed },
            label: "0.00",
          },
          eds,
        ),
      )
    },
    [setEdges],
  )

  return (
    <div className="w-full h-full border border-gray-300 rounded-md">
      <ReactFlow
        nodes={nodes}
        edges={edges}
        onNodesChange={onNodesChange}
        onEdgesChange={onEdgesChange}
        onConnect={onConnect}
        fitView
      >
        <Controls />
        <MiniMap />
        <Background variant={BackgroundVariant.Dots} color="#ffffff" />
      </ReactFlow>
    </div>
  )
}
