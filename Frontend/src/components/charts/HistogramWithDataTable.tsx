import React, { useEffect, useState } from 'react';
import { Table, Input, Space, Button, Divider, Checkbox, CheckboxOptionType } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { ApexOptions } from 'apexcharts';
import ReactApexChart from 'react-apexcharts';

interface DataPoint {
  Time: string;
  [key: string]: string;
}

interface HistogramWithDataTableProps {
  data: DataPoint[];
}

export const HistogramWithDataTable: React.FC<HistogramWithDataTableProps> = ({ data }) => {
  const [searchText, setSearchText] = useState('');
  console.log(searchText);
  const [searchedColumn, setSearchedColumn] = useState('');
  const [checkedList, setCheckedList] = useState<string[]>([]);
  const [columns, setColumns] = useState<any[]>([]); // Initial columns state

  useEffect(() => {
    if (data.length > 0) {
      const cols = [
        {
          title: 'Date',
          dataIndex: 'Time',
          key: 'Time',
          width: 300,
          sorter: (a: any, b: any) => dayjs(a.Time).unix() - dayjs(b.Time).unix(),
          ...getColumnSearchProps('Time'),
        },
        ...Object.keys(data[0])
          .filter((key) => key !== 'date' && key !== 'key' && key !== 'Time')
          .map((key) => ({
            title: key,
            dataIndex: key,
            key: key,
            width: 300,
            sorter: (a: any, b: any) => a[key] - b[key],
            ...getColumnSearchProps(key),
          })),
      ];
      setColumns(cols);
      setCheckedList(cols.map((item) => item.key)); // Set default checked list based on columns
    }
  }, [data]);

  if (data.length === 0) return <div>No data available</div>;

  /*


    Functions to handle table data mnaipulations


  */

  // Set up search functionality for each column
  const getColumnSearchProps = (dataIndex: string) => ({
    filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }: any) => (
      <div style={{ padding: 8 }}>
        <Input
          placeholder={`Search ${dataIndex}`}
          value={selectedKeys[0]}
          onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
          onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
          style={{ marginBottom: 8, display: 'block' }}
        />
        <Space>
          <Button
            type="primary"
            onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
            icon={<SearchOutlined />}
            size="small"
            style={{ width: 90 }}
          >
            Search
          </Button>
          <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
            Reset
          </Button>
        </Space>
      </div>
    ),
    filterIcon: (filtered: boolean) => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
    onFilter: (value: any, record: any) =>
      record[dataIndex]
        ? record[dataIndex]
          .toString()
          .toLowerCase()
          .includes((value as string).toLowerCase())
        : '',
    render: (text: any) => (searchedColumn === dataIndex ? <span>{text}</span> : text),
  });

  const handleSearch = (selectedKeys: string[], confirm: () => void, dataIndex: string) => {
    confirm();
    setSearchText(selectedKeys[0]);
    setSearchedColumn(dataIndex);
  };

  const handleReset = (clearFilters: () => void) => {
    clearFilters();
    setSearchText('');
  };

  // Define the columns for the table dynamically based on the first data point
  // const columns = [
  //   {
  //     title: 'Date',
  //     dataIndex: 'Time',
  //     key: 'Time',
  //     width: 300,
  //     sorter: (a: any, b: any) => dayjs(a.Time).unix() - dayjs(b.Time).unix(),
  //     ...getColumnSearchProps('Time'),
  //   },
  //   ...Object.keys(data[0])
  //     .filter((key) => key !== 'date' && key !== 'key' && key !== 'Time')
  //     .map((key) => ({
  //       title: key,
  //       dataIndex: key,
  //       key: key,
  //       width: 300,
  //       sorter: (a: any, b: any) => a[key] - b[key],
  //       ...getColumnSearchProps(key),
  //     })),
  // ];


  const newColumns = columns.map((item) => ({
    ...item,
    hidden: !checkedList.includes(item.key as string),
  }));
  const options = columns?.map(({ key, title }) => ({
    label: title,
    value: key,
  }));

  /*


  Functions to handle histograms mnaipulations


*/

  // Helper function to create bins for histograms
  const createBins = (values: number[], numBins: number = 10) => {
    // Filter out any non-numeric values or NaN
    const filteredValues = values.filter((val) => !isNaN(val));
    if (filteredValues.length === 0) return []; // Return empty if no valid values

    const minVal = Math.min(...filteredValues);
    const maxVal = Math.max(...filteredValues);
    const binSize = (maxVal - minVal) / numBins;

    const bins = Array.from({ length: numBins }, (_, i) => ({
      rangeStart: minVal + i * binSize,
      rangeEnd: minVal + (i + 1) * binSize,
      count: 0,
    }));

    filteredValues.forEach((val) => {
      const binIndex = Math.floor((val - minVal) / binSize);
      const validIndex = Math.min(binIndex, bins.length - 1); // Handle edge case for max value
      bins[validIndex].count++;
    });

    return bins;
  };

  // Parsing 'Time' field with updated format
  const dateValues = data.map((item) => new Date(item.Time).getTime());
  const dateBins = createBins(dateValues, 10);

  const dateOptions: ApexOptions = {
    chart: {
      type: 'bar',
      height: 200,
      sparkline: {
        enabled: true,
      },
    },
    xaxis: {
      categories: dateBins.map((bin, i) => {
        const startDate = new Date(bin.rangeStart).toLocaleString();
        const endDate = new Date(bin.rangeEnd).toLocaleString();
        return i === 0 || i === dateBins.length - 1 ? `${startDate} - ${endDate}` : '';
      }),
      labels: {
        show: true,
      },
    },
    plotOptions: {
      bar: {
        columnWidth: '100%',
        distributed: false,
      },
    },
    fill: {
      colors: ['#1E90FF'],
    },
    dataLabels: {
      enabled: false,
    },
    series: [
      {
        name: 'Frequency',
        data: dateBins.map((bin) => bin.count),
      },
    ],
  };

  const histogramComponents = Object.keys(data[0])
    .filter((key) => key !== 'Time') // Exclude 'Time' field
    .map((key) => {
      // Convert string values to floats and filter out non-numeric ones
      const values = data.map((item) => parseFloat(item[key])).filter((val) => !isNaN(val));
      const bins = createBins(values, 10);

      if (bins.length === 0) return null; // Skip if no valid bins

      const options: ApexOptions = {
        chart: {
          type: 'bar',
          height: 200,
          sparkline: {
            enabled: true,
          },
        },
        xaxis: {
          categories: bins.map((bin, i) => {
            const rangeStart = bin.rangeStart.toFixed(2);
            const rangeEnd = bin.rangeEnd.toFixed(2);
            return i === 0 || i === bins.length - 1 ? `${rangeStart} - ${rangeEnd}` : '';
          }),
          labels: {
            show: true,
          },
        },
        plotOptions: {
          bar: {
            columnWidth: '100%',
            distributed: false,
          },
        },
        fill: {
          colors: ['#1E90FF'],
        },
        dataLabels: {
          enabled: false,
        },
        series: [
          {
            name: 'Frequency',
            data: bins.map((bin) => bin.count),
          },
        ],
      };

      return (
        <div key={key} style={{ flexShrink: 0, width: '300px', padding: '0 10px', height: '100%' }}>
          <ReactApexChart options={options} series={[{ data: bins.map((bin) => bin.count) }]} type="bar" height={200} />
          <div style={{ textAlign: 'center', marginTop: '5px' }}>
          <strong>{key}</strong>
        </div>
        </div>
      );
    });

  return (
    <div style={{ display: 'flex', flexDirection: 'column', padding: '20px', width: '100%' }}>
      <h2>Data Table with Histograms</h2>

      {/* Histogram*/}
      <div style={{ display: 'flex', overflowX: 'auto', overflowY: 'hidden', padding: '10px 0', width: '100%' }}>
        <div style={{ flexShrink: 0, width: '300px', padding: '0 10px', height: '100%' }}>
          <ReactApexChart options={dateOptions} series={[{ data: dateBins.map((bin) => bin.count) }]} type="bar" height={200} />
          <div style={{ textAlign: 'center', marginTop: '5px' }}>
          <strong>Overall Date Graph</strong>
        </div>
        </div>
        {histogramComponents}
      </div>
      { }
      <div style={{ width: '100%', overflowX: 'auto', background: 'white' }}>
        <Divider>Columns displayed</Divider>
        <Checkbox.Group
          value={checkedList}
          options={options as CheckboxOptionType[]}
          onChange={(value) => {
            setCheckedList(value as string[]);
          }}
          style={{ color: 'white', background: 'white', padding: '3px' }}
        />
        <Table
          columns={newColumns}
          dataSource={data}
          pagination={{ pageSize: 11 }}
          scroll={{ x: 'max-content' }}
          tableLayout="fixed"
        />
      </div>
    </div>
  );
};

/*

Code for histogram to be inside the row but filters were not working so added custom filter options for it
Needs more work on custom functionality


*/

// import React, { useState, useEffect } from 'react';
// import { Table, Input, Space, Button } from 'antd';
// import { CaretDownOutlined, CaretUpOutlined, SearchOutlined } from '@ant-design/icons';
// import dayjs from 'dayjs';
// import { ApexOptions } from 'apexcharts';
// import ReactApexChart from 'react-apexcharts';

// interface DataPoint {
//   Time: string;
//   [key: string]: string;
// }

// interface HistogramWithDataTableProps {
//   data: DataPoint[];
// }

// export const HistogramWithDataTable: React.FC<HistogramWithDataTableProps> = ({ data }) => {
//   const [searchText, setSearchText] = useState('');
//   const [searchedColumn, setSearchedColumn] = useState('');
//   const [sortOrder, setSortOrder] = useState<{ [key: string]: 'asc' | 'desc' | undefined }>({});
//   const [filteredData, setFilteredData] = useState<DataPoint[]>(data); // Initialize with prop data

//   // Update filteredData when data prop changes
//   useEffect(() => {
//     setFilteredData(data);
//   }, [data]);

//   const handleSort = (dataIndex: string) => {
//     const currentOrder = sortOrder[dataIndex] || 'asc'; // Get current sort order or default to 'asc'
//     const newOrder = currentOrder === 'asc' ? 'desc' : 'asc'; // Toggle order

//     setSortOrder({
//       ...sortOrder,
//       [dataIndex]: newOrder, // Update the sort order for the specific column
//     });

//     // Sort the data based on the selected column and order
//     const sortedData = [...filteredData].sort((a, b) => {
//       const aValue = a[dataIndex];
//       const bValue = b[dataIndex];

//       if (newOrder === 'asc') {
//         return aValue > bValue ? 1 : -1; // Ascending
//       } else {
//         return aValue < bValue ? 1 : -1; // Descending
//       }
//     });

//     setFilteredData(sortedData); // Update the filtered data with sorted data
//   };

//   if (data.length === 0) return <div>No data available</div>;

//   const getColumnSearchProps = (dataIndex: string) => ({
//     filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }: any) => (
//       <div style={{ padding: 8 }}>
//         <Input
//           placeholder={`Search ${dataIndex}`}
//           value={selectedKeys[0]}
//           onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
//           onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
//           style={{ marginBottom: 8, display: 'block' }}
//         />
//         <Space>
//           <Button
//             type="primary"
//             onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
//             icon={<SearchOutlined />}
//             size="small"
//             style={{ width: 90 }}
//           >
//             Search
//           </Button>
//           <Button onClick={() => handleReset(clearFilters)} size="small" style={{ width: 90 }}>
//             Reset
//           </Button>
//         </Space>
//       </div>
//     ),
//     filterIcon: (filtered: boolean) => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
//     onFilter: (value: any, record: any) =>
//       record[dataIndex]
//         ? record[dataIndex]
//             .toString()
//             .toLowerCase()
//             .includes((value as string).toLowerCase())
//         : '',
//     render: (text: any) => (searchedColumn === dataIndex ? <span>{text}</span> : text),
//   });

//   const handleSearch = (selectedKeys: string[], confirm: () => void, dataIndex: string) => {
//     confirm();
//     setSearchText(selectedKeys[0]);
//     setSearchedColumn(dataIndex);
//   };

//   const handleReset = (clearFilters: () => void) => {
//     clearFilters();
//     setSearchText('');
//   };

//   const columns = [
//     {
//       title: 'Date',
//       dataIndex: 'Time',
//       key: 'Time',
//       width: 300,
//       sorter: (a: any, b: any) => dayjs(a.Time).unix() - dayjs(b.Time).unix(),
//       ...getColumnSearchProps('Time'),
//     },
//     ...Object.keys(data[0])
//       .filter((key) => key !== 'Time')
//       .map((key) => ({
//         title: key,
//         dataIndex: key,
//         key: key,
//         width: 300,
//         sorter: (a: any, b: any) => parseFloat(a[key]) - parseFloat(b[key]),
//         ...getColumnSearchProps(key),
//       })),
//   ];

//   const createBins = (values: number[], numBins: number = 10) => {
//     const filteredValues = values.filter((val) => !isNaN(val));
//     if (filteredValues.length === 0) return [];

//     const minVal = Math.min(...filteredValues);
//     const maxVal = Math.max(...filteredValues);
//     const binSize = (maxVal - minVal) / numBins;

//     const bins = Array.from({ length: numBins }, (_, i) => ({
//       rangeStart: minVal + i * binSize,
//       rangeEnd: minVal + (i + 1) * binSize,
//       count: 0,
//     }));

//     filteredValues.forEach((val) => {
//       const binIndex = Math.floor((val - minVal) / binSize);
//       const validIndex = Math.min(binIndex, bins.length - 1);
//       bins[validIndex].count++;
//     });

//     return bins;
//   };

//   const dateValues = filteredData.map((item) => new Date(item.Time).getTime());
//   const dateBins = createBins(dateValues, 10);

//   const dateOptions: ApexOptions = {
//     chart: {
//       type: 'bar',
//       height: 200,
//       sparkline: {
//         enabled: true,
//       },
//     },
//     xaxis: {
//       categories: dateBins.map((bin) => `${new Date(bin.rangeStart).toLocaleString()} - ${new Date(bin.rangeEnd).toLocaleString()}`),
//       labels: { show: true },
//     },
//     plotOptions: { bar: { columnWidth: '100%', distributed: false } },
//     fill: { colors: ['#1E90FF'] },
//     dataLabels: { enabled: false },
//     series: [{ name: 'Frequency', data: dateBins.map((bin) => bin.count) }],
//   };

//   const histogramComponents: { [key: string]: React.ReactNode } = {};
//   Object.keys(data[0])
//     .filter((key) => key !== 'Time')
//     .forEach((key) => {
//       const values = filteredData.map((item) => parseFloat(item[key])).filter((val) => !isNaN(val));
//       const bins = createBins(values, 10);

//       const options: ApexOptions = {
//         chart: { type: 'bar', height: 200, sparkline: { enabled: true } },
//         xaxis: {
//           categories: bins.map((bin) => `${bin.rangeStart.toFixed(2)} - ${bin.rangeEnd.toFixed(2)}`),
//           labels: { show: true },
//         },
//         plotOptions: { bar: { columnWidth: '100%', distributed: false } },
//         fill: { colors: ['#1E90FF'] },
//         dataLabels: { enabled: false },
//         series: [{ name: 'Frequency', data: bins.map((bin) => bin.count) }],
//       };

//       histogramComponents[key] = (
//         <ReactApexChart options={options} series={[{ data: bins.map((bin) => bin.count) }]} type="bar" height={200} />
//       );
//     });

//   const NewGetColumnSearchProps = (dataIndex: any) => {
//     return (
//       <div>
//         <Space>
//           <Button
//             icon={<CaretUpOutlined />}
//             onClick={() => handleSort(dataIndex)} // Call handleSort on click
//             size="small"
//           />
//           <Button
//             icon={<CaretDownOutlined />}
//             onClick={() => handleSort(dataIndex)} // Call handleSort on click
//             size="small"
//           />
//         </Space>
//       </div>
//     );
//   };

//   const tableHeaderWithLabels = (
//     <>
//       {/* Column Labels Row */}
//       <tr>
//         <th key="label-Time" style={{ width: '300px' }}>
//           Date
//           {NewGetColumnSearchProps('Time')}
//         </th>
//         {Object.keys(data[0])
//           .filter((key) => key !== 'Time')
//           .map((key) => (
//             <th key={`label-${key}`} style={{ width: '300px' }}>
//               {key}
//               {NewGetColumnSearchProps(key)}
//             </th>
//           ))}
//       </tr>

//       {/* Graph Row */}
//       <tr>
//         <th key="graph-Time" style={{ width: '300px' }}>
//           {<ReactApexChart options={dateOptions} series={[{ data: dateBins.map((bin) => bin.count) }]} type="bar" height={200} />}
//         </th>
//         {Object.keys(data[0])
//           .filter((key) => key !== 'Time')
//           .map((key) => (
//             <th key={`graph-${key}`} style={{ width: '300px' }}>
//               {histogramComponents[key]}
//             </th>
//           ))}
//       </tr>
//     </>
//   );

//   return (
//     <div style={{ display: 'flex', flexDirection: 'column', padding: '20px', width: '100%' }}>
//       <h2>Data Table with Histograms</h2>
//       <Table
//         columns={columns}
//         dataSource={filteredData} // Use filteredData for the table
//         pagination={{ pageSize: 11 }}
//         scroll={{ x: 'max-content' }}
//         tableLayout="fixed"
//         components={{
//           header: {
//             row: () => tableHeaderWithLabels
//           },
//         }}
//       />
//     </div>
//   );
// };