import React, { useState } from 'react';
import ReactSelect, { MultiValue, Props } from 'react-select';

// Custom type declaration to fix TypeScript issues
type SelectComponent = <Option, IsMulti extends boolean = false>(
  props: Props<Option, IsMulti>
) => JSX.Element;

const Select = ReactSelect as SelectComponent;

interface MaterialConfig {
  selectedParameters: string[];
  parameterValues: {
    [key: string]: string;
  };
}

interface GlobalConfig {
  [material: string]: MaterialConfig;
}

interface Option {
  value: string;
  label: string;
}

interface ParameterSection {
  title: string;
  parameters: string[];
}

const parameterSections: ParameterSection[] = [
  {
    title: "Time",
    parameters: [
      "fillingStartTime",
      "fillingStopTime",
      "cultureAdditionTime",
      "rennetAdditionTime",
      "coagulationStartTime",
      "coagulationStopTime",
      "emptyingStartTime",
      "emptyingStopTime",
      "cuttingStartTime",
      "DateTime"
    ]
  },
  {
    title: "Quantity & Volume",
    parameters: [
      "cultureQuantity",
      "rennetQuantity",
      "milkQuantityInVat"
    ]
  },
  {
    title: "Generic Identifiers",
    parameters: [
      "sequenceNo",
      "vatNo",
      "recipeNo"
    ]
  },
  {
    title: "Derived Parameters",
    parameters: [
      "residenceTimeInVat",
      "totalProcesstimeInVat",
      "emptyingDuration",
      "cookingDuration",
      "coagulationDuration"
    ]
  },
  {
    title: "Temperature",
    parameters: [
      "temperatureInThermiser",
      "temperatureOfMilkInVat"
    ]
  },
  {
    title: "Flow & Rate & Speed",
    parameters: [
      "fillingRPM",
      "cuttingRPM",
      "intermittentCuttingRPM"
    ]
  },
  {
    title: "Material Quality",
    parameters: [
      "cultureType"
    ]
  }
];

interface ProcessStep {
  name: string;
  selectedParameters: string[];
}

const processParameters = [
  { value: 'fillingStartTime', label: 'Filling Start Time' },
  { value: 'fillingStopTime', label: 'Filling Stop Time' },
  { value: 'cultureQuantity', label: 'Culture Quantity' },
  { value: 'cultureAdditionTime', label: 'Culture Addition Time' },
  { value: 'rennetQuantity', label: 'Rennet Quantity' },
  { value: 'rennetAdditionTime', label: 'Rennet Addition Time' },
  { value: 'coagulationStartTime', label: 'Coagulation Start Time' },
  { value: 'coagulationStopTime', label: 'Coagulation Stop Time' },
  { value: 'emptyingStartTime', label: 'Emptying Start Time' },
  { value: 'emptyingStopTime', label: 'Emptying Stop Time' },
  { value: 'cuttingStartTime', label: 'Cutting Start Time' },
  { value: 'sequenceNo', label: 'Sequence No' },
  { value: 'vatNo', label: 'Vat No' },
  { value: 'recipeNo', label: 'Recipe No' },
  { value: 'milkQuantityInVat', label: 'Milk Quantity in Vat' },
  { value: 'residenceTimeInVat', label: 'Residence Time in Vat' },
  { value: 'totalProcesstimeInVat', label: 'Total Process Time in Vat' },
  { value: 'emptyingDuration', label: 'Emptying Duration' },
  { value: 'cookingDuration', label: 'Cooking Duration' },
  { value: 'coagulationDuration', label: 'Coagulation Duration' },
  { value: 'temperatureInThermiser', label: 'Temperature in Thermiser' },
  { value: 'DateTime', label: 'Date Time' },
  { value: 'temperatureOfMilkInVat', label: 'Temperature of Milk in Vat' },
  { value: 'fillingRPM', label: 'Filling RPM' },
  { value: 'cuttingRPM', label: 'Cutting RPM' },
  { value: 'intermittentCuttingRPM', label: 'Intermittent Cutting RPM' },
  { value: 'cultureType', label: 'Culture Type' }
];

const processSteps = [
  'Milk Standardisation',
  'Pasteurisation',
  'Heating of Milk',
  'Coagulation',
  'Cutting',
  'Cooking',
  'Stirring',
  'Whey Draining',
  'Milling',
  'Salting',
  'Draining and Pressing',
  'Ripening'
];

// Add these interfaces
interface SystemConfig {
  name: string;
  configurations: {
    parameters: {
      [key: string]: 'Setpoints' | 'MeasuredValueOfSetpoints' | 'MeasuredButNotControlled' | 'MeasuredCanBeControlled' | 'NeitherMeasureNorControlled'
    };
    targetVariables: {
      parameter: string;
      range: {
        min: number;
        max: number;
      };
    }[];
  };
  inputMaterials: string[];
  processes: string[];
  output?: string;
  byProduct?: string;
}

// Add static data for systems
const systemsData = [
  {
    name: 'Environmental',
    parameters: ['temperature', 'humidity']
  },
  {
    name: 'Thermiser',
    inputMaterials: ['Pasteurised Milk'],
    processes: ['DateTime', 'Heating of Milk'],
    output: 'Heated Milk',
    byProduct: null
  },
  {
    name: 'VAT',
    inputMaterials: ['Heated Milk', 'Renet', 'Culture'],
    processes: [
      'TimeParameter',
      'Generic Identifiers',
      'Other Parameters',
      'Culture Addition',
      'Renet Addition',
      'Coagulation',
      'Cooking',
      'Cutting',
      'Heating',
      'Stirring',
      'Emptying',
      'DerivedParameters'
    ],
    output: 'Curd',
    byProduct: 'Whey'
  },
  {
    name: 'Alfamatic',
    inputMaterials: ['Curd', 'Iodised Salt with Caking agent'],
    processes: ['Whey Draining', 'Salting'],
    output: 'MilledCurdWithoutWhey',
    byProduct: 'Whey'
  },
  {
    name: 'Blockformer',
    inputMaterials: ['Milled Curd Without Whey'],
    processes: ['DrainingAndPressing'],
    output: 'Cheddar Cheese Block',
    byProduct: null
  }
];

const parameterCategories = [
  'Setpoints',
  'MeasuredValueOfSetpoints',
  'MeasuredButNotControlled',
  'MeasuredCanBeControlled',
  'NeitherMeasureNorControlled'
] as const;

const CheddarCheeseConfig: React.FC = () => {
  const [currentSection, setCurrentSection] = useState<number>(0);
  const sections = [
    'Materials Involved During Process',
    'Process Steps',
    'Other Parameter Types',
    'Output Materials Involved',
    'ByProducts Involved',
    'Systems'
  ];

  // Available materials and quality parameters
  const availableMaterials = [
    { value: 'Pasteurised Milk', label: 'Pasteurised Milk' },
    { value: 'Heated Milk', label: 'Heated Milk' },
    { value: 'Curd After Coagulation', label: 'Curd After Coagulation' },
    { value: 'Renet', label: 'Renet' },
    { value: 'Culture', label: 'Culture' },
    { value: 'Milled Curd Without Whey', label: 'Milled Curd Without Whey' },
    { value: 'Iodised Salt With Caking Agent', label: 'Iodised Salt With Caking Agent' },
    { value: 'Cheddar Cheese Block', label: 'Cheddar Cheese Block' },
    { value: 'Whey', label: 'Whey' }
  ];

  const qualityParameters = [
    { value: 'pH', label: 'pH' },
    { value: 'Salt%', label: 'Salt%' },
    { value: 'IV', label: 'IV' },
    { value: 'Moisture%', label: 'Moisture%' },
    { value: 'FFA', label: 'FFA' },
    { value: 'Density', label: 'Density' },
    { value: 'SnF', label: 'SnF' }
  ];

  // State for selected materials and their configurations
  const [selectedMaterials, setSelectedMaterials] = useState<string[]>([]);
  const [globalConfig, setGlobalConfig] = useState<GlobalConfig>({});
  const [selectedParameters, setSelectedParameters] = useState<string[]>([]);
  const [selectedOutputMaterials, setSelectedOutputMaterials] = useState<string[]>([]);
  const [selectedByProducts, setSelectedByProducts] = useState<string[]>([]);
  const [processConfigs, setProcessConfigs] = useState<{ [key: string]: string[] }>({});

  // Add to your component's state
  const [selectedSystems, setSelectedSystems] = useState<string[]>([]);
  const [systemConfigs, setSystemConfigs] = useState<{ [key: string]: SystemConfig }>({});

  // Handle material selection
  const handleMaterialChange = (selected: MultiValue<Option>) => {
    const selectedValues = selected ? selected.map(item => item.value) : [];
    setSelectedMaterials(selectedValues);
    
    // Initialize config for newly selected materials
    const newConfig = { ...globalConfig };
    selectedValues.forEach((material:any) => {
      if (!newConfig[material]) {
        newConfig[material] = {
          selectedParameters: [],
          parameterValues: {}
        };
      }
    });
    
    // Remove config for unselected materials
    Object.keys(newConfig).forEach(material => {
      if (!selectedValues.includes(material)) {
        delete newConfig[material];
      }
    });
    
    setGlobalConfig(newConfig);
  };

  // Handle parameter selection for a material
  const handleParameterChange = (material: string, selected: MultiValue<Option>) => {
    const selectedValues = selected ? selected.map(item => item.value) : [];
    
    setGlobalConfig(prev => ({
      ...prev,
      [material]: {
        ...prev[material],
        selectedParameters: selectedValues,
        parameterValues: {
          ...Object.fromEntries(
            selectedValues.map((param:any) => [
              param,
              prev[material]?.parameterValues[param] || ''
            ])
          )
        }
      }
    }));
  };

  // Handle parameter value change
  const handleParameterValueChange = (
    material: string,
    parameter: string,
    value: string
  ) => {
    setGlobalConfig(prev => ({
      ...prev,
      [material]: {
        ...prev[material],
        parameterValues: {
          ...prev[material].parameterValues,
          [parameter]: value
        }
      }
    }));
  };

  const handleOtherParameterChange = (parameter: string) => {
    setSelectedParameters(prev => 
      prev.includes(parameter)
        ? prev.filter(p => p !== parameter)
        : [...prev, parameter]
    );
  };

  const renderMaterialsInvolved = () => (
    <div className="space-y-8">
      <h2 className="text-2xl font-bold mb-6">Input Materials Involved During Process</h2>
      
      <div className="mb-8">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Select Materials
        </label>
        <Select<Option, true>
          isMulti
          options={availableMaterials}
          value={availableMaterials.filter(opt => 
            selectedMaterials.includes(opt.value)
          )}
          onChange={handleMaterialChange}
          className="basic-multi-select"
          classNamePrefix="select"
        />
      </div>

      {selectedMaterials.map(material => (
        <div key={material} className="p-6 border rounded-lg bg-gray-50">
          <h3 className="text-lg font-semibold mb-4">{material}</h3>
          
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Quality Parameters
            </label>
            <Select<Option, true>
              isMulti
              options={qualityParameters}
              value={qualityParameters.filter(opt =>
                globalConfig[material]?.selectedParameters.includes(opt.value)
              )}
              onChange={(selected) => handleParameterChange(material, selected)}
              className="basic-multi-select"
              classNamePrefix="select"
            />
          </div>

          {globalConfig[material]?.selectedParameters.map(parameter => (
            <div key={parameter} className="mt-4">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                {parameter}
              </label>
              <input
                type="text"
                value={globalConfig[material]?.parameterValues[parameter] || ''}
                onChange={(e) => handleParameterValueChange(
                  material,
                  parameter,
                  e.target.value
                )}
                className="w-full p-2 border rounded-md"
                placeholder={`Enter ${parameter} value`}
              />
            </div>
          ))}
        </div>
      ))}
    </div>
  );

  const renderOtherParameterTypes = () => (
    <div className="space-y-8">
      <h2 className="text-2xl font-bold mb-6">Other Parameter Types</h2>
      
      {parameterSections.map((section) => (
        <div key={section.title} className="p-6 border rounded-lg bg-gray-50">
          <h3 className="text-lg font-semibold mb-4">{section.title}</h3>
          
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {section.parameters.map((parameter) => (
              <label key={parameter} className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  className="w-4 h-4 text-blue-600 rounded border-gray-300 focus:ring-blue-500"
                  checked={selectedParameters.includes(parameter)}
                  onChange={() => handleOtherParameterChange(parameter)}
                />
                <span className="text-sm">{parameter}</span>
              </label>
            ))}
          </div>
        </div>
      ))}
    </div>
  );

  const renderOutputMaterials = () => (
    <div className="space-y-8">
      <h2 className="text-2xl font-bold mb-6">Output Materials Involved</h2>
      
      <div className="p-6 border rounded-lg bg-gray-50">
        <div className="mb-8">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Select Output Materials
          </label>
          <Select<Option, true>
            isMulti
            options={availableMaterials}
            value={availableMaterials.filter(opt => 
              selectedOutputMaterials.includes(opt.value)
            )}
            onChange={(selected) => {
              const selectedValues = selected ? selected.map(item => item.value) : [];
              setSelectedOutputMaterials(selectedValues);
            }}
            className="basic-multi-select"
            classNamePrefix="select"
          />
        </div>

        {selectedOutputMaterials.length > 0 && (
          <div className="mt-6">
            <h3 className="text-md font-medium mb-3">Selected Output Materials:</h3>
            <div className="space-y-2">
              {selectedOutputMaterials.map(material => (
                <div key={material} className="p-3 bg-white rounded border">
                  {material}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );

  const renderByProducts = () => (
    <div className="space-y-8">
      <h2 className="text-2xl font-bold mb-6">ByProducts Involved</h2>
      
      <div className="p-6 border rounded-lg bg-gray-50">
        <div className="mb-8">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Select ByProducts
          </label>
          <Select<Option, true>
            isMulti
            options={availableMaterials}
            value={availableMaterials.filter(opt => 
              selectedByProducts.includes(opt.value)
            )}
            onChange={(selected) => {
              const selectedValues = selected ? selected.map(item => item.value) : [];
              setSelectedByProducts(selectedValues);
            }}
            className="basic-multi-select"
            classNamePrefix="select"
          />
        </div>

        {selectedByProducts.length > 0 && (
          <div className="mt-6">
            <h3 className="text-md font-medium mb-3">Selected ByProducts:</h3>
            <div className="space-y-2">
              {selectedByProducts.map(product => (
                <div key={product} className="p-3 bg-white rounded border">
                  {product}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );

  const renderProcessSteps = () => (
    <div className="space-y-8">
      <h2 className="text-2xl font-bold mb-6">Process Steps</h2>
      
      {processSteps.map((step) => (
        <div key={step} className="p-6 border rounded-lg bg-gray-50">
          <h3 className="text-lg font-semibold mb-4">{step}</h3>
          
          <div className="mb-8">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Select Parameters
            </label>
            <Select<Option, true>
              isMulti
              options={processParameters}
              value={processParameters.filter(opt => 
                processConfigs[step]?.includes(opt.value)
              )}
              onChange={(selected) => {
                const selectedValues = selected ? selected.map(item => item.value) : [];
                setProcessConfigs(prev => ({
                  ...prev,
                  [step]: selectedValues
                }));
              }}
              className="basic-multi-select"
              classNamePrefix="select"
            />
          </div>

          {processConfigs[step]?.length > 0 && (
            <div className="mt-6">
              <h4 className="text-md font-medium mb-3">Selected Parameters:</h4>
              <div className="space-y-2">
                {processConfigs[step].map(param => (
                  <div key={param} className="p-3 bg-white rounded border">
                    {processParameters.find(p => p.value === param)?.label}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      ))}
    </div>
  );

  // Add the render function for Systems
  const renderSystems = () => (
    <div className="space-y-8">
      <h2 className="text-2xl font-bold mb-6">Systems Configuration</h2>

      {/* System Selection */}
      <div className="p-6 border rounded-lg bg-gray-50">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Select Systems
        </label>
        <Select<Option, true>
          isMulti
          options={systemsData.map(sys => ({ value: sys.name, label: sys.name }))}
          value={systemsData
            .filter(sys => selectedSystems.includes(sys.name))
            .map(sys => ({ value: sys.name, label: sys.name }))}
          onChange={(selected) => {
            const selectedValues = selected ? selected.map(item => item.value) : [];
            setSelectedSystems(selectedValues);
          }}
          className="basic-multi-select"
          classNamePrefix="select"
        />
      </div>

      {/* Individual System Configurations */}
      {selectedSystems.map(systemName => {
        const system = systemsData.find(s => s.name === systemName);
        if (!system) return null;

        return (
          <div key={systemName} className="p-6 border rounded-lg bg-gray-50">
            <h3 className="text-xl font-semibold mb-6">{systemName}</h3>

            {systemName === 'Environmental' ? (
              // Environmental System
              <div className="space-y-4">
                {system?.parameters?.map(param => (
                  <div key={param} className="flex items-center space-x-4">
                    <span className="text-sm font-medium">{param}</span>
                    <input
                      type="number"
                      className="w-32 p-2 border rounded-md"
                      placeholder={`Enter ${param}`}
                    />
                  </div>
                ))}
              </div>
            ) : (
              // Other Systems
              <div className="space-y-6">
                {/* Parameter Configuration */}
                <div className="mb-6">
                  <h4 className="text-lg font-medium mb-4">Parameter Categories</h4>
                  <div className="space-y-4">
                    {processParameters.map(param => (
                      <div key={param.value} className="flex items-center space-x-4">
                        <span className="w-1/3">{param.label}</span>
                        <Select<Option, false>
                          options={parameterCategories.map(cat => ({
                            value: cat,
                            label: cat
                          }))}
                          value={
                            systemConfigs[systemName]?.configurations.parameters[param.value]
                              ? {
                                  value: systemConfigs[systemName].configurations.parameters[param.value],
                                  label: systemConfigs[systemName].configurations.parameters[param.value]
                                }
                              : null
                          }
                          onChange={(selected) => {
                            if (!selected) return;
                            setSystemConfigs(prev => ({
                              ...prev,
                              [systemName]: {
                                ...prev[systemName],
                                configurations: {
                                  ...prev[systemName]?.configurations,
                                  parameters: {
                                    ...prev[systemName]?.configurations.parameters,
                                    [param.value]: selected.value as typeof parameterCategories[number]
                                  }
                                }
                              }
                            }));
                          }}
                          className="w-2/3"
                        />
                      </div>
                    ))}
                  </div>
                </div>

                {/* Target Variables */}
                <div className="mb-6">
                  <h4 className="text-lg font-medium mb-4">Target Variables</h4>
                  <div className="space-y-4">
                    {qualityParameters.map(param => (
                      <div key={param.value} className="grid grid-cols-3 gap-4 items-center">
                        <label className="flex items-center space-x-2">
                          <input
                            type="checkbox"
                            className="w-4 h-4 text-blue-600 rounded"
                            checked={systemConfigs[systemName]?.configurations.targetVariables.some(
                              t => t.parameter === param.value
                            )}
                            onChange={(e) => {
                              setSystemConfigs(prev => {
                                const newConfig = { ...prev };
                                if (!newConfig[systemName]) {
                                  newConfig[systemName] = {
                                    name: systemName,
                                    configurations: {
                                      parameters: {},
                                      targetVariables: []
                                    },
                                    inputMaterials: system.inputMaterials as any,
                                    processes: system.processes as any,
                                    output: system.output,
                                    byProduct: system.byProduct as any
                                  };
                                }

                                if (e.target.checked) {
                                  newConfig[systemName].configurations.targetVariables.push({
                                    parameter: param.value,
                                    range: { min: 0, max: 0 }
                                  });
                                } else {
                                  newConfig[systemName].configurations.targetVariables = 
                                    newConfig[systemName].configurations.targetVariables.filter(
                                      t => t.parameter !== param.value
                                    );
                                }
                                return newConfig;
                              });
                            }}
                          />
                          <span>{param.label}</span>
                        </label>
                        {systemConfigs[systemName]?.configurations.targetVariables.some(
                          t => t.parameter === param.value
                        ) && (
                          <>
                            <input
                              type="number"
                              placeholder="Min"
                              className="p-2 border rounded-md"
                              value={systemConfigs[systemName]?.configurations.targetVariables.find(
                                t => t.parameter === param.value
                              )?.range.min || ''}
                              onChange={(e) => {
                                setSystemConfigs(prev => {
                                  const newConfig = { ...prev };
                                  const targetVar = newConfig[systemName].configurations.targetVariables.find(
                                    t => t.parameter === param.value
                                  );
                                  if (targetVar) {
                                    targetVar.range.min = Number(e.target.value);
                                  }
                                  return newConfig;
                                });
                              }}
                            />
                            <input
                              type="number"
                              placeholder="Max"
                              className="p-2 border rounded-md"
                              value={systemConfigs[systemName]?.configurations.targetVariables.find(
                                t => t.parameter === param.value
                              )?.range.max || ''}
                              onChange={(e) => {
                                setSystemConfigs(prev => {
                                  const newConfig = { ...prev };
                                  const targetVar = newConfig[systemName].configurations.targetVariables.find(
                                    t => t.parameter === param.value
                                  );
                                  if (targetVar) {
                                    targetVar.range.max = Number(e.target.value);
                                  }
                                  return newConfig;
                                });
                              }}
                            />
                          </>
                        )}
                      </div>
                    ))}
                  </div>
                </div>

                {/* System Information */}
                <div className="grid grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-lg font-medium mb-4">Input Materials</h4>
                    <ul className="list-disc list-inside space-y-2">
                      {system?.inputMaterials?.map(material => (
                        <li key={material}>{material}</li>
                      ))}
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-lg font-medium mb-4">Processes</h4>
                    <ul className="list-disc list-inside space-y-2">
                      {system?.processes?.map(process => (
                        <li key={process}>{process}</li>
                      ))}
                    </ul>
                  </div>
                </div>

                {system.output && (
                  <div>
                    <h4 className="text-lg font-medium mb-4">Output</h4>
                    <p>{system.output}</p>
                  </div>
                )}

                {system.byProduct && (
                  <div>
                    <h4 className="text-lg font-medium mb-4">ByProduct</h4>
                    <p>{system.byProduct}</p>
                  </div>
                )}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );

  // Navigation Functions
  const handleNext = () => {
    if (currentSection < sections.length - 1) {
      setCurrentSection(prev => prev + 1);
    }
  };

  const handleBack = () => {
    if (currentSection > 0) {
      setCurrentSection(prev => prev - 1);
    }
  };

  const handleSave = () => {
    const allConfigurations = {
      materialsInvolved: globalConfig,
      processSteps: processConfigs,
      otherParameters: selectedParameters,
      outputMaterials: selectedOutputMaterials,
      byProducts: selectedByProducts,
      systems: systemConfigs
    };
    console.log('All Configurations:', allConfigurations);
  };

  // Section Render Functions
  const renderPlaceholderSection = (title: string) => (
    <div className="space-y-8">
      <h2 className="text-2xl font-bold mb-6">{title}</h2>
      <p>This section is under development</p>
    </div>
  );

  // Main render function
  const renderCurrentSection = () => {
    switch (currentSection) {
      case 0:
        return renderMaterialsInvolved();
      case 1:
        return renderProcessSteps();
      case 2:
        return renderOtherParameterTypes();
      case 3:
        return renderOutputMaterials();
      case 4:
        return renderByProducts();
      case 5:
        return renderPlaceholderSection('Systems');
        // return renderSystems();
      default:
        return null;
    }
  };

  return (
    <div className="max-w-6xl mx-auto">
      {/* Progress indicator */}
      <div className="mb-6 flex justify-between items-center">
        <span className="text-sm text-gray-500">
          Step {currentSection + 1} of {sections.length}
        </span>
        <span className="text-lg font-medium">{sections[currentSection]}</span>
      </div>

      {/* Current Section Content */}
      {renderCurrentSection()}

      {/* Navigation Buttons */}
      <div className="mt-8 flex justify-between">
        <button
          onClick={handleBack}
          className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700"
          disabled={currentSection === 0}
        >
          Back
        </button>

        {currentSection === sections.length - 1 ? (
          <button
            onClick={handleSave}
            className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
          >
            Save Configuration
          </button>
        ) : (
          <button
            onClick={handleNext}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700"
          >
            Next
          </button>
        )}
      </div>
    </div>
  );
};

export default CheddarCheeseConfig;