import React, { useState, useEffect } from 'react';
import { message, But<PERSON>, Switch, Modal, Popconfirm } from 'antd';
import { getRequest, postRequest } from '../../utils/apiHandler';
import { useAuth } from '../../context/AuthContext';
import { ReloadOutlined } from '@ant-design/icons';
import { useNavigate, useLocation } from 'react-router-dom';

const TwoFactorAuth: React.FC = () => {
  const navigate = useNavigate();
  const [isEnabled, setIsEnabled] = useState<boolean>(false);
  const [qrCode, setQrCode] = useState<string>('');
  const [secret, setSecret] = useState<string>('');
  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);
  const [verificationCode, setVerificationCode] = useState<string>('');
  const [isReconfiguring, setIsReconfiguring] = useState<boolean>(false);
  const { authState, updateFirstLoginStatus } = useAuth();

  // Effect to update first login status
  // useEffect(() => {
  //   if (authState.user?.is_first_login) {
  //     console.log("First login detected, will update status after 2FA setup");

  //     // If it's the first login, automatically show the setup modal
  //     const autoSetup = async () => {
  //       try {
  //         const response = await getRequest('/auth/2fa/setup');
  //         if (response.data.status === 200) {
  //           setQrCode(response.data.data.qrCode);
  //           setSecret(response.data.data.secret);
  //           setIsModalVisible(true);
  //         }
  //       } catch (error) {
  //         console.error('Failed to auto-setup 2FA:', error);
  //       }
  //     };

  //     // autoSetup();
  //   }
  // // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [authState.user?.is_first_login]);

  // Effect to check 2FA status
  useEffect(() => {
    // Check if 2FA is already enabled for the user
    const check2FAStatus = async () => {
      try {
        const response = await getRequest('/auth/2fa/status');
        if (response.data.status === 200) {
          setIsEnabled(response.data.data.isEnabled);

          // If 2FA is not enabled and it's the user's first login, automatically trigger setup
          if (!response.data.data.isEnabled && authState.user?.is_first_login) {
            handleSetup2FA();
          }
        }
      } catch (error) {
        console.error('Failed to fetch 2FA status:', error);
        message.error('Failed to fetch 2FA status');
      }
    };

    check2FAStatus();
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Function to handle automatic 2FA setup for first login
  const handleSetup2FA = async () => {
    try {

        // Also update the first login status if it's the first login
        if (authState.user?.is_first_login) {
          try {
            console.log("Updating first login status...");
            const updateResponse = await postRequest('/auth/update-first-login', {});
            console.log("Update response:", updateResponse);

            if (updateResponse.data.status === 200) {
              console.log("First login status updated successfully");

              // Update auth context state
              // updateFirstLoginStatus(false);
            }
          } catch (updateError) {
            console.error('Failed to update first login status:', updateError);
          }
      }
    } catch (error) {
      console.error('Failed to setup 2FA:', error);
      message.error('Failed to setup 2FA');
    }
  };

  const handleToggle = async (checked: boolean) => {
    if (checked) {
      // Enable 2FA
      try {
        const response = await getRequest('/auth/2fa/setup');
        if (response.data.status === 200) {
          setQrCode(response.data.data.qrCode);
          setSecret(response.data.data.secret);
          setIsModalVisible(true);
        }
      } catch (error) {
        console.error('Failed to setup 2FA:', error);
        message.error('Failed to setup 2FA');
      }
    } else {
      // Disable 2FA
      try {
        const response = await postRequest('/auth/2fa/disable', {});
        if (response.data.status === 200) {
          setIsEnabled(false);
          message.success('Two-factor authentication disabled successfully');
        }
      } catch (error) {
        console.error('Failed to disable 2FA:', error);
        message.error('Failed to disable 2FA');
      }
    }
  };

  const handleVerify = async () => {
    try {
      const response = await postRequest('/auth/2fa/verify', {
        token: verificationCode,
        secret: secret
      });

      if (response.data.status === 200) {
        setIsEnabled(true);
        setIsModalVisible(false);
        setIsReconfiguring(false);
        setVerificationCode('')
        // If this was the first login, update the is_first_login flag
        if (authState.user?.is_first_login) {
          try {
            console.log("Updating first login status after verification...");
            // Update the is_first_login flag to false
            const updateResponse = await postRequest('/auth/update-first-login', {});
            console.log("Update response after verification:", updateResponse);

            if (updateResponse.data.status === 200) {
              console.log("First login status updated successfully after verification");

              // Update auth context state
              updateFirstLoginStatus(false);

              message.success('Two-factor authentication enabled successfully');
              return; // Exit early since we're redirecting
            }
          } catch (updateError) {
            console.error('Failed to update first login status:', updateError);
          }
        }

        message.success('Two-factor authentication enabled successfully');
      } else {
        message.error('Invalid verification code');
      }
    } catch (error) {
      console.error('Failed to verify 2FA code:', error);
      message.error('Failed to verify 2FA code');
    }
  };

  const handleReconfigure = async () => {
    try {
      // First disable current 2FA
      await postRequest('/auth/2fa/disable', {});

      // Then setup new 2FA
      const response = await getRequest('/auth/2fa/setup');
      if (response.data.status === 200) {
        setQrCode(response.data.data.qrCode);
        setSecret(response.data.data.secret);
        setIsModalVisible(true);
        setIsReconfiguring(true);
        message.info('Please set up your new authenticator app');
      }
    } catch (error) {
      console.error('Failed to reconfigure 2FA:', error);
      message.error('Failed to reconfigure 2FA');
    }
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow">
      <button 
          onClick={() => navigate('/')}
          className="px-4 py-2 mb-4 bg-primary text-white rounded hover:opacity-80"
        >
          Go to Dashboard
        </button>
        <div className="max-w-2xl mx-auto p-2">
           <h1 className="text-2xl font-semibold mt-4">Security Settings</h1>
           <p className="text-gray-600 mt-1">Manage your account security preferences and authentication methods</p>

          {/*   only for first time user */}
           {authState.user?.is_first_login && (
            <div className="mb-4 p-4 rounded-xl shadow  bg-blue-50 border-l-4 border-blue-500 text-blue-700">
              <p className="font-bold">You're in!</p>
              <p>Let’s make your account extra secure with two-factor authentication. We highly recommend setting it up now.</p>
              <p>Please follow the instructions below to complete the setup.</p>
            </div>
          )} 


           {/*   Two-Factor Authentication  */}
            <div className="bg-white rounded-xl shadow mt-6 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-lg font-semibold">Two-Factor Authentication(2FA)</h2>
                  <div className="mt-4 flex items-center space-x-2">
                    <span className="text-sm font-medium">Enable Two-Factor Authentication With Authenticator App</span>
                    {/* "text-sm text-gray-500">Secure your account with an authenticator app</span> */}
                  </div>
                  <p className="text-sm text-gray-600 mt-1">
                    Enhance your account security by enabling two-factor authentication. Once enabled, you'll need to provide a verification code in addition to your password when signing in.
                  </p>
                  {isEnabled && (
                  <div className="mt-4 flex items-center space-x-2">
                    <span className="text-sm font-medium">Two-Factor Authentication Recovery</span>
                  <Popconfirm
                    title="Reconfigure 2FA"
                    description="This will reset your current 2FA setup. You'll need to scan a new QR code with your authenticator app. Continue?"
                    onConfirm={handleReconfigure}
                    okText="Yes"
                    cancelText="No"
                  >
                    <Button
                      type="default"
                      icon={<ReloadOutlined />}
                      className="ml-4"
                      title="Lost your device? Reconfigure 2FA"
                    >
                      Reconfigure
                    </Button>
                  </Popconfirm>
                  </div>
                  )}
                  {isEnabled && (
                  <p className="text-sm text-gray-600 mt-1">
                  If you want to reconfigure your two-factor authentication or no longer have access to your authentication device.
                  </p>
                  )}
                </div>
                <label className="inline-flex items-center cursor-pointer">
                  {/* <input type="checkbox" className="sr-only peer" /> */}
                  <Switch
                  checked={isEnabled}
                  onChange={handleToggle}
                  className={isEnabled ? "bg-green-500" : ""}
                />
                </label>
              </div>
            </div>

            {/*   Email Authentication  */}
            <div className="bg-white rounded-xl shadow mt-4 p-6">
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-lg font-semibold">Email Authentication</h2>
                  <p className="text-sm text-gray-600 mt-1">
                    Receive a one-time verification code via email when signing .
                  </p>
                  <div className="mt-4 flex items-center space-x-2">
                    <span className="text-sm font-medium">Enable Email Authentication</span>
                    <span className="text-sm text-gray-500">Receive verification codes via email</span>
                  </div>
                </div>
                <label className="inline-flex items-center cursor-pointer">
                  <Switch
                    checked={true}
                    className='bg-green-500'
                  />
                </label>
              </div>
              {/* <p className="text-sm text-gray-500 mt-4">Your current email: <EMAIL> </p> */}
            </div>
      </div>

      <Modal
        title={isReconfiguring ? "Reconfigure Two-Factor Authentication" : "Setup Two-Factor Authentication"}
        open={isModalVisible}
        onCancel={() => {
          setIsModalVisible(false);
          setVerificationCode('');
          if (isReconfiguring) {
            const check2FAStatus = async () => {
              try {
                const response = await getRequest('/auth/2fa/status');
                if (response.data.status === 200) {
                  setIsEnabled(response.data.data.isEnabled);
                }
              } catch (error) {
                console.error('Failed to fetch 2FA status:', error);
              }
            };
            check2FAStatus();
            setIsReconfiguring(false);
          }
        }}
        footer={null} // Move buttons inside the form
      >
        <form
          onSubmit={(e) => {
            e.preventDefault();
            if (verificationCode.length === 6) {
              handleVerify();
            }
          }}
        >
          <div className="text-center">
            <p className="mb-4">
              {isReconfiguring
                ? "Scan this new QR code with your authenticator app to reconfigure your 2FA."
                : "Scan this QR code with your authenticator app (like Google Authenticator, Authy, or Microsoft Authenticator)."}
            </p>

            {qrCode && (
              <div className="flex justify-center mb-4">
                <img src={qrCode} alt="QR Code for 2FA" className="border p-2" />
              </div>
            )}

            <p className="mb-2 font-medium">Manual setup code:</p>
            <p className="mb-4 bg-gray-100 p-2 rounded font-mono">{secret}</p>

            <div className="mt-4">
              <p className="mb-2">Enter the verification code from your authenticator app:</p>
              <input
                type="text"
                className="border rounded p-2 w-full text-center text-lg tracking-wider"
                value={verificationCode}
                onChange={(e) => setVerificationCode(e.target.value)}
                placeholder="000000"
                maxLength={6}
              />
            </div>

            <div className="flex justify-end mt-6 space-x-2">
              <Button
                key="cancel"
                onClick={() => {
                  setIsModalVisible(false);
                  setVerificationCode('');
                  if (isReconfiguring) {
                    const check2FAStatus = async () => {
                      try {
                        const response = await getRequest('/auth/2fa/status');
                        if (response.data.status === 200) {
                          setIsEnabled(response.data.data.isEnabled);
                        }
                      } catch (error) {
                        console.error('Failed to fetch 2FA status:', error);
                      }
                    };
                    check2FAStatus();
                    setIsReconfiguring(false);
                  }
                }}
              >
                Cancel
              </Button>
              <Button
                key="verify"
                type="primary"
                htmlType="submit"
                className={`text-white ${verificationCode.length !== 6 ? 'opacity-50 pointer-events-none' : ''}`}
              >
                Verify
              </Button>
            </div>
          </div>
        </form>
      </Modal>

    </div>
  );
};

export default TwoFactorAuth;
