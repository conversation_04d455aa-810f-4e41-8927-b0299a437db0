import  { ChangeEvent, useRef, useState, KeyboardEvent, ClipboardEvent } from "react";
import icon from '../../img/Icon.svg'
import { useNavigate, useLocation } from 'react-router-dom';
import { message, Modal, Input } from 'antd';
import { patchRequest, postRequest } from "../../utils/apiHandler";

type ErrorType = {
    password?: string;
    confirmPassword?: string;
  }

const ForgotPasswordScreen = () => {
const [email, setEmail] = useState('');
  const [step, setStep] = useState(1); // 0: login, 1: forgot, 2: otp, 3: reset, 4: success
  const navigate = useNavigate();
  const [otp, setOtp] = useState<string[]>(['', '', '', '', '', '']);
  const inputRefs = useRef<Array<HTMLInputElement | null>>([]);
  const emailRef = useRef<HTMLInputElement>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [formData, setFormData] = useState({
    password: '',
    confirmPassword: '',
  });
  const [formError, setFormError] = useState<ErrorType>({});

  const handleChangePassword = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target;
  
    setFormData((prev) => ({ ...prev, [id]: value }));
    setFormError((prev) => ({ ...prev, [id]: '' }));
  };
  

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
  
    const { password, confirmPassword } = formData;
    const errors: ErrorType = {};
  
    const hasMinLength = password?.length >= 8;
    const hasSpecialChar = /[!@#$%^&*(),.?":{}|<>]/.test(password || '');
  
    if (!password) {
      errors.password = 'Please enter a new password.';
    } else {
      if (!hasMinLength) {
        errors.password = 'Password must be at least 8 characters long.';
      }
      if (!hasSpecialChar) {
        errors.password = 'Password must include at least one special character.';
      }
    }
  
    if (!confirmPassword) {
      errors.confirmPassword = 'Please confirm your password.';
    } else if (password && password !== confirmPassword) {
      errors.confirmPassword = 'Passwords do not match.';
    }
  
    // Set form errors if any
    if (Object.keys(errors).length > 0) {
      setFormError(errors);
      return;
    }
  
    // Clear any existing errors before proceeding
    setFormError({});
  
    // Proceed to API call or next step
    console.log("Password reset successfully:", password);
    try {
        let data={
            email:email,
            newPassword:password
        }
        const response = await patchRequest('/auth/change-password',data)
        if(response.status=200){
            setStep(4);
            message.success('Password has been changed successfully');
        }
        console.log('response :', response);
        
    } catch (error:any) {
        message.error(error.response.data.message || 'Failed to changed password');
    }
  };
  



  const handleChange = (e: ChangeEvent<HTMLInputElement>, index: number): void => {
    const value = e.target.value;

    if (value && !/^\d+$/.test(value)) return;

    const newOtp = [...otp];
    newOtp[index] = value.slice(0, 1);
    setOtp(newOtp);

    if (value && index < 5 && inputRefs.current[index + 1]) {
      inputRefs.current[index + 1]?.focus();
    }
  };

  const handleKeyDown = (e: KeyboardEvent<HTMLInputElement>, index: number): void => {
    if (e.key === 'Backspace') {
      if (!otp[index] && index > 0) {
        const newOtp = [...otp];
        newOtp[index - 1] = '';
        setOtp(newOtp);
        inputRefs.current[index - 1]?.focus();
      }
    }
  };

  const handlePaste = (e: ClipboardEvent<HTMLInputElement>): void => {
    e.preventDefault();
    const pastedData = e.clipboardData.getData('text').trim();

    if (/^\d{6}$/.test(pastedData)) {
      const newOtp = pastedData.split('');
      setOtp(newOtp);
      inputRefs.current[5]?.focus();
    }
  };


  const handleSendVerificationCode = async (e: { preventDefault: () => void; }) => {
    e.preventDefault(); // Prevent default form submission
    setLoading(true)
    const rawEmail = email.trim() || '';

    // Basic email format validation
    const isValidEmail = /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(rawEmail);

    if (!rawEmail) {
        message.error('Please enter your email.');
        setLoading(false)
      return;
    }

    if (!isValidEmail) {
        message.error('Please enter a valid email address.');
        setLoading(false)
      return;
    }

    try {
        let data={
            email:rawEmail
        }

        const response = await postRequest('/auth/forget-password',data)
        console.log('response :', response);
        if(response.status==200){
            message.success(response?.data?.message)
            setStep(2);
            setLoading(false)
        }else{
            setLoading(false)
            message.error('Failed to send OTP');
        }
        
    } catch (error:any) {
    console.log('error :', error);
    message.error(error.response.data.message || 'Failed to send OTP');
    setLoading(false)
    }
  };


  const verifyForgetPasswordOtp = async (e: { preventDefault: () => void; }) => {
    e.preventDefault();
    const otpValue = otp.join('');
  
    if (otpValue.length !== 6) {
      message.error('Please enter a complete 6-digit OTP');
      return;
    }
  
    const user = {
      email: email.trim() || '',
      otp: otp,
    };
  
    setLoading(true);
    try {
      
      const response = await postRequest('/auth/verify-forget-password-otp', user);
  
      console.log('✅ OTP verification response:', response);
  
      if (response.status === 200) {
        message.success('OTP verified successfully');
        setStep(3);
        setLoading(false)
      } else {
        message.error(response?.data?.message || 'Failed to verify OTP. Please try again.');
        setLoading(false)
      }
    } catch (error: any) {
    console.log('error :', error?.response?.data.message);
    message.error(error?.response?.data.message || 'Failed to verify OTP. Please try again.');
    setLoading(false)
      
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-[#f5f5f8]">
      <div className="bg-white rounded-lg p-8 w-[400px] shadow-md">
        <div className="flex justify-center mb-8">
            <img src={icon} className='h-20' alt='logo'/>
        </div>

        {step === 1 && (
          <>
            <h2 className="text-lg font-semibold mb-2">Forgot Password</h2>
            <p className="text-sm text-gray-500 mb-4">
              Please enter your email address to receive a verification code
            </p>
            <form onSubmit={handleSendVerificationCode}>
            <div className="mb-4">
              <label htmlFor="recovery-email" className="block text-gray-700 font-medium mb-1">
                Email
              </label>
              <input
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                placeholder="Enter your email"
                className="w-full p-2 mb-4 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-400"
              />
            </div>
            <button
              type="submit"
              className="w-full py-3 bg-[#2d2253] text-white rounded text-base font-medium"
              disabled={loading}
            >
              Send OTP
            </button>
            </form>
            <button
              className="text-sm text-[#2d2253] hover:underline mt-4 w-full text-center"
              onClick={() => navigate('/login')}
            >
              Back to LogIn
            </button>
          </>
        )}

        {step === 2 && (
          <>
            <h2 className="text-lg font-semibold mb-2">Enter Verification Code</h2>
            <p className="text-sm text-gray-500 mb-4">
              Please enter the verification code sent to your email
            </p>
            <form
            onSubmit={verifyForgetPasswordOtp}
            >
            <div className="flex gap-2 mb-4">
            {otp.map((digit, index) => (
                  <input
                    key={index}
                    ref={(el) => (inputRefs.current[index] = el)}
                    type="text"
                    value={digit}
                    onChange={(e) => handleChange(e, index)}
                    onKeyDown={(e) => handleKeyDown(e, index)}
                    onPaste={index === 0 ? handlePaste : undefined}
                    className="w-12 h-12 text-center text-lg font-semibold 
                               border border-gray-300 rounded-lg
                               focus:border-blue-500 focus:ring-1 focus:ring-blue-500
                               focus:outline-none transition-all"
                    maxLength={1}
                    autoComplete="off"
                  />
                ))}
            </div>
            <button
              type="submit"
              className="w-full py-3 bg-[#2d2253] text-white rounded text-base font-medium"
              disabled={loading}
            >
              Verify Code
            </button>
            </form>
            <button className="text-sm text-[#2d2253] hover:underline mt-4 w-full text-center"
            onClick={(e) => handleSendVerificationCode(e)}
            disabled={loading}>
            
              Resend Code
            </button>
            <button className="text-sm text-[#2d2253] hover:underline mt-4 w-full text-center"
            onClick={(e) => setStep(1)}
            disabled={loading}>
            
              Back to change Email
            </button>
          </>
        )}

        {step === 3 && (
          <>
          <h2 className="text-lg font-semibold mb-2">Set New Password</h2>
    
          <form onSubmit={handleSubmit}>
            <div className="mb-4">
              <label htmlFor="password" className="block text-gray-700 font-medium mb-1">
                New Password
              </label>
              <input
                type="password"
                id="password"
                value={formData.password}
                onChange={handleChangePassword}
                placeholder="Enter new password"
                className="w-full p-2 mb-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-400"
              />
              {formError.password && <div className="text-red-700 text-sm">{formError.password}</div>}
            </div>
    
            <div className="mb-4">
              <label htmlFor="confirmPassword" className="block text-gray-700 font-medium mb-1">
                Confirm Password
              </label>
              <input
                type="password"
                id="confirmPassword"
                value={formData.confirmPassword}
                onChange={handleChangePassword}
                placeholder="Confirm new password"
                className="w-full p-2 mb-1 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-400"
              />
              {formError.confirmPassword && (
                <div className="text-red-700 text-sm">{formError.confirmPassword}</div>
              )}
            </div>
    
            <button
              type="submit"
              className="w-full py-3 bg-[#2d2253] text-white rounded text-base font-medium"
            >
              Reset Password
            </button>
          </form>
          <button className="text-sm text-[#2d2253] hover:underline mt-4 w-full text-center"
            onClick={(e) => navigate('/login')}
            >
              Return to Login
            </button>
        </>
        )}

        {step === 4 && (
          <>
            <h2 className="text-lg font-semibold mb-2 text-center">
              Password Reset Successful
            </h2>
            <p className="text-sm text-gray-500 text-center mb-6">
              Your password has been reset successfully!
            </p>
            <button
              className="w-full py-3 bg-[#2d2253] text-white rounded text-base font-medium"
              onClick={() => navigate('/login')}
            >
              Log In
            </button>
          </>
        )}
      </div>
    </div>
  );
};

export default ForgotPasswordScreen;