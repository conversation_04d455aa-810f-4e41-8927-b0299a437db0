import React, { useEffect, useState } from 'react';
import crossIcon from "../../../img/cross.svg";
import InsightCausalInference from './InsightCausalInference';
import { Table, Tabs, message } from 'antd';
import { Notify } from 'notiflix';
import { getRequest, postRequest } from '../../../utils/apiHandler';

interface InsightRunDeviationDataProps {
    runData: any;
    cellData: any;
    setCellData: (value: any) => void;
    selectedWorkflowId: string | null;
    setIsCollapsed: (value: boolean) => void;
    mlJobRunId: string;
    columns: any[];
    selectedCellsData: any;
    setResetCheckboxes: (value: boolean) => void;
}

const InsightRunDeviationData: React.FC<InsightRunDeviationDataProps> = ({
    runData,
    cellData,
    setCellData,
    selectedWorkflowId,
    setIsCollapsed,
    mlJobRunId,
    columns,
    selectedCellsData,
    setResetCheckboxes
}) => {
    const [inferenceTasks, setInferenceTasks] = useState<any>({});
    const [comment, setComment] = useState<any>(null);
    const [selectedTenantUser, setSelectedTenantUser] = useState<any>(null);
    const [tenantConnectUsers, setTenantConnectUsers] = useState<any>(null);

    const handleNewTask = async () => {
        if (!selectedCellsData || !Array.isArray(selectedCellsData) || selectedCellsData.length === 0) {
            message.error('No data available to create tasks');
            return;
        }

        if (!selectedTenantUser) {
            message.error('Need to assign User');
            return;
        }

        for (const data of selectedCellsData) {
            let payload = {
                header_value: data.header,
                cell_value: data.value,
                date_time: data.DateTime,
                assign_to: selectedTenantUser,
                comment: comment,
                generic_identifier: {
                    sequenceNo: data.sequenceNo,
                    recipeNo: data.recipeNo,
                    vatNo: data.vatNo,
                },
                run_deviation_data: data.run_deviation_data
                    ? Object.fromEntries(
                        Object.entries(data.run_deviation_data).filter(
                            ([key, value]) =>
                                !key.endsWith("%") && typeof value !== "boolean"
                        )
                    )
                    : null,
            };

            console.log('payload', payload);

            try {
                let response = await postRequest('/tasks', payload);
                console.log('response', response);

                if (response.status === 201) {
                    message.success(`Task created successfully for ${data.header}`);
                } else {
                    message.error(`Failed to create task for ${data.header}`);
                }
            } catch (error) {
                console.error('Error creating task:', error);
                message.error(`Error creating task for ${data.header}`);
            }
        }

        // Reset states after all tasks are created
        setIsCollapsed(false);
        setCellData(null);
        setSelectedTenantUser(null);
        setComment(null);

        setResetCheckboxes(true)

    };

    useEffect(() => {
        getConnectedTenants();
    }, [])

    const getConnectedTenants = async () => {
        try {
            const response = await getRequest(`/workflow/connected-tenants`);
            console.log('response', response)
            if (response?.data && response?.data?.data) {
                console.log('responseeee', response?.data?.data)
                setTenantConnectUsers(response?.data?.data)
            }
        } catch (error) {
            console.log('error', error)
            setTenantConnectUsers(null)
        }
    }

    return (
        <div
            className="h-full max-h-[643px] bg-[#f6f6f6]"
            style={{
                minWidth: "420px",
            }}
        >
            {runData && (
                <>
                    <div className="flex items-center p-2 border-b gap-2">
                        <h3 className="text-lg font-bold justify-center m-0">Run Deviation Data</h3>
                        <img
                            src={crossIcon}
                            alt="crossIcon"
                            className='w-4 h-4 cursor-pointer ms-auto'
                            onClick={() => {
                                setIsCollapsed(false)
                                setResetCheckboxes(true)
                            }}
                        />
                    </div>
                    <div
                        className="p-4 pt-0 overflow-y-auto max-h-[573px]"
                        style={{
                            height: "92%"
                        }}
                    >
                        <Tabs defaultActiveKey="1" className="w-full h-full" type='card'>
                            <Tabs.TabPane tab="Data" key="1">
                                <Table columns={columns} dataSource={runData} pagination={false} />
                            </Tabs.TabPane>
                            <Tabs.TabPane tab="Causal Inference" key="2">
                                <div className='flex justify-center h-[500px]'>
                                    <InsightCausalInference runData={runData} selectedWorkflowId={selectedWorkflowId} mlJobRunId={mlJobRunId} inferenceTasks={inferenceTasks} setInferenceTasks={setInferenceTasks} />
                                </div>
                            </Tabs.TabPane>
                        </Tabs>
                    </div>
                </>
            )}
            {cellData && (
                <>
                    <div className="flex items-center p-2 border-b">

                        <h3 className="text-lg font-bold justify-center m-0">Create Task </h3>
                        <img
                            src={crossIcon}
                            alt="crossIcon"
                            className='w-4 h-4 cursor-pointer ms-auto'
                            onClick={() => { setIsCollapsed(false) }}
                        />
                    </div>
                    <div
                        className="p-4 overflow-y-auto max-h-[600px]"
                        style={{
                            height: "92%"
                        }}
                    >
                        <div className="bg-white rounded-lg shadow">
                            <div className="divide-y divide-gray-200">
                                <div>
                                    {Object.entries(cellData)
                                        .filter(([key, value]) => key !== 'run_deviation_data')
                                        .map(([key, value]) => (
                                            <div
                                                key={key}
                                                className="flex justify-between items-center p-4 hover:bg-gray-50 cursor-pointer"
                                            >
                                                <span className="text-gray-600">{key}</span>
                                                <span className="font-medium">{String(value)}</span>
                                            </div>
                                        ))}
                                </div>
                                <div className="flex justify-between items-center p-4 hover:bg-gray-50 cursor-pointer">
                                    <span className="text-gray-600">Assign To</span>
                                    <span className="font-medium">
                                        <select
                                            value={selectedTenantUser}
                                            onChange={(e) => setSelectedTenantUser(e.target.value)}
                                            className="block w-full outline-none shadow-none px-3 py-2 bg-white border border-gray-300 rounded-md"
                                        >
                                            <option value="">Select User</option>
                                            {tenantConnectUsers?.map((users: any) => (
                                                <option key={users} value={users?.id}>
                                                    {users?.first_name}
                                                    {' '}
                                                    {users?.last_name}
                                                </option>
                                            ))}
                                        </select>
                                    </span>
                                </div>
                            </div>
                        </div>
                        <div className="space-y-4 mt-2">
                            <div>
                                <textarea
                                    value={comment}
                                    onChange={(e) => setComment(e.target.value)}
                                    placeholder="Write a comment..."
                                    className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                />
                            </div>

                            <button
                                onClick={handleNewTask}
                                className="w-full px-4 py-2 text-white bg-primary rounded-md hover:bg-primary focus:outline-none focus:ring-2 focus:bg-primary focus:ring-offset-2 transition-colors"
                            >
                                Create Task
                            </button>
                        </div>

                    </div>
                </>
            )}
        </div>
    );
};

export default InsightRunDeviationData;