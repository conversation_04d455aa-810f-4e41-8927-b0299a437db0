import { Button, Flex, Skeleton, message } from 'antd';
import React, { useEffect } from 'react';
import Plot from "react-plotly.js";
import { getRequest, postRequest } from '../../../utils/apiHandler';
import { Dot<PERSON>hartOutlined, ReloadOutlined } from '@ant-design/icons';
import { update } from 'plotly.js';
import Notiflix, { Notify } from 'notiflix';

interface InsightCausalInferenceProps {
    runData: any;
    selectedWorkflowId: string | null;
    mlJobRunId: string;
    inferenceTasks: any[];
    setInferenceTasks: React.Dispatch<React.SetStateAction<any[]>>;
}

const InsightCausalInference: React.FC<InsightCausalInferenceProps> = (
    { runData, selectedWorkflowId, mlJobRunId, inferenceTasks, setInferenceTasks }
) => {
    const [loading, setLoading] = React.useState(false);
    const [showPlot, setShowPlot] = React.useState(false);
    const [Xseries, setXseries] = React.useState<any[]>([]);
    const [Yseries, setYseries] = React.useState<any[]>([]);

    useEffect(() => {
        console.log('inferenceTasks', inferenceTasks)
        const runDate = runData.find((data: any) => data.key === "DateTime").value.toString();
        console.log('runDate', runDate)
        let inferenceFound = false;
        // Check if inference data exists for this run date in inferenceTasks
        Object.entries(inferenceTasks).forEach(([key, value]) => {
            if (key === runDate) {
                // Check if inference data loaded.
                if ( "complete" === value.loading ) {
                    setXseries(value.xSeries);
                    setYseries(value.ySeries);
                    setShowPlot(true);
                } else {
                    setLoading(true);
                    setShowPlot(false);

                }
                inferenceFound = true;
            }
        });

        // If inference data exists for this run date, set loading to false and showPlot to true
        if ( !inferenceFound ) {
            setLoading(false);
            setShowPlot(false);
            setXseries([]);
            setYseries([]);
        }
    }, [runData]);

    const formatRunData = (array: any[]) => {
        const obj: any = {};
        array.forEach((item) => {
            // Parse all values as float and skip "DateTime" key
            if (item.key !== "DateTime") {
                obj[item.key] = parseFloat(item.value);
            }
        });
        return obj;
    };

    const getCausalInference = async (event: any) => {
        setLoading(true);
        try {
            // Get new causal inference.
            const payload = {
                outlier: [formatRunData(runData)],
                target_col: "vatpH",
                s3_url_model: "s3://com.tvarit.banas.datalake.consolidated.prod-eu-central-1/data/data=file/CAUSAL_MODEL/BASE_TEST/fitted_causal_prod.pkl",
                workflow_id: selectedWorkflowId,
                run_id: mlJobRunId,
            }
            const runDate = runData.find((data: any) => data.key === "DateTime").value.toString();
            // console.log('payload', payload)
            setInferenceTasks(prev => ({
                ...prev,
                [runDate]: {
                    loading: "in_progress",
                    xSeries: null,
                    ySeries: null
                }
            }
            ));

            const response = await postRequest('/causal-inference/outlier-inference', payload)

            if (response.status == 200) {
                // Notify.success(`Inference data fetched successfully for run date: ${runDate}`);
                message.success(`Inference data fetched successfully for run date: ${runDate}`);
                // console.log('response', response.data[0])
                // Update plot data
                updateInferencePlotData(response.data[0], runDate);
                setShowPlot(true);
            } else {
                // Notify.failure(response.data.message)
                message.error(response.data.message);
            }
        } catch (error) {
            console.error('Error fetching causal inference data:', error);
            // Notiflix.Notify.failure('Failed to fetch causal inference data');
            message.error('Failed to fetch causal inference data');
        }
        setLoading(false);
    }

    const updateInferencePlotData = (inference: any, runDate: string) => {
        let xSeries: any[] = [];
        let ySeries: any[] = [];
        Object.keys(inference).forEach((key) => {
            // Skip values whose absolute value is lesser than 0.01, does not contain letter 'e' or the key is vatpH
            // console.log('key', key, inference[key])
            if (
                // Math.abs(inference[key]) < 0.01 ||  // Uncomment this line to filter out values with absolute value less than 0.01
                // inference[key].includes('e') || // Uncomment this line to filter out values containing 'e'. Mainly for responses returned in string format
                key == 'vatpH') {
                return;
            }

            // Add key and value to Xseries and Yseries arrays
            xSeries.push(key);
            ySeries.push(inference[key]);
            setXseries((prev) => [...prev, key]);
            setYseries((prev) => [...prev, inference[key]]);
        });

        // Update inferenceTasks state with the new data
        setInferenceTasks(prev => ({
            ...prev,
            [runDate]: {
                loading: "complete",
                xSeries,
                ySeries,
            }
        }));
    }

    return (
        <div>
            {showPlot && (
                <Flex gap={"middle"} vertical className='p-2 mt-0' align='flex-start'>
                    <Plot
                        className='p-1'
                        data={[
                            {
                                x: Yseries,
                                y: Xseries,
                                type: 'bar',
                                mode: 'lines+markers',
                                marker: { color: 'red' },
                                orientation: 'h',
                            },
                        ]}
                        layout={{
                            width: 400,
                            height: 620,
                            title: 'Causal Inference',
                            xaxis: { title: 'Causal Inference', side: 'top' },
                            yaxis: { title: 'Feature', automargin: true },
                            margin: { t: 50, b: 50, l: 50, r: 50 },
                        }}
                        config={{ displayModeBar: false }}
                    />
                </Flex>
            )}
            {!showPlot && (
                <Flex gap={"middle"} vertical className='p-2 mt-2' align='center' justify='space-between' style={{ width: '100%' }}>
                    <Button type='primary' onClick={getCausalInference} icon={<DotChartOutlined />} loading={loading}>
                        Fetch Causal Inference
                    </Button>
                    {loading && (
                        <>
                            <Skeleton.Node active={loading} className='h-full flex items-center justify-center p-4 ml-5 mt-4'>
                                <DotChartOutlined style={{ fontSize: 40, color: '#bfbfbf' }} />
                            </Skeleton.Node>
                        </>
                    )}
                </Flex>
            )}
        </div>
    );
};

export default InsightCausalInference;