export type TabType = 'batch' | 'plc';

export interface Tab {
    id: string;
    label: string;
    icon: string; // 'ts' | 'js' | etc.
    content?: string;
    active: boolean;
    modified: boolean;
    tabType: TabType; // Add tab type to track batch vs PLC
  }
  
  export interface ContextMenuState {
    isVisible: boolean;
    x: number;
    y: number;
    tabId: string;
  }
  
  export type ContextMenuAction = 
    | "closeTab" 
    | "closeAllTabs" 
    | "closeOtherTabs" 
    | "closeTabsToRight"
  