import React, { useEffect, useMemo, useRef, useState } from 'react';
import { ColumnSelection, DateFilter } from '../types';
import { PanelFilter } from '../FilterTypes';
import { Spin, Empty, Tabs } from 'antd';
import Plot from 'react-plotly.js';

interface HistogramPanelProps {
  data: any; // Original complete data
  filteredData?: any; // Pre-filtered data
  selectedColumns?: ColumnSelection;
  dateFilter?: DateFilter;
  isLoading?: boolean;
  panelFilters?: PanelFilter[];
  onZoomSelection?: (column: string, min: number, max: number, sourcePanelId?: string) => void;
}

interface ColumnHistogram {
  name: string;
  values: number[];
  bins: {
    x: number;
    y: number;
  }[];
  min: number;
  max: number;
}

// eslint-disable-next-line @typescript-eslint/no-unused-vars
const HistogramPanel: React.FC<HistogramPanelProps> = ({
  data,
  filteredData,
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  selectedColumns = { indices: [], headers: [] },
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  dateFilter = { startDate: null, endDate: null }, // Keep for interface compatibility
  isLoading = false,
  panelFilters = [],
  onZoomSelection
}) => {

  const containerRef = useRef<HTMLDivElement>(null);
  const [plotHeight, setPlotHeight] = useState(250); // default fallback

  useEffect(() => {
    const container = containerRef.current;
    if (!container) return;

    const resizeObserver = new ResizeObserver(entries => {
      for (let entry of entries) {
        const height = entry.contentRect.height;
        setPlotHeight(Math.floor(height * 0.65));
      }
    });

    resizeObserver.observe(container);

    return () => resizeObserver.disconnect();
  }, []);




  // Extract numeric columns from data and calculate histograms
  const columnHistograms = useMemo(() => {
    console.log('HistogramPanel - useMemo triggered');
    console.log('HistogramPanel - panelFilters:', panelFilters);

    // Use filteredData if available, otherwise use original data
    const dataToProcess = filteredData || data;

    if (!dataToProcess || isLoading) return [];

    try {
      // Handle different data formats
      let headers: string[] = [];
      let rows: any[] = [];

      // Extract headers and rows from data
      if (Array.isArray(dataToProcess)) {
        if (dataToProcess.length > 0) {
          headers = Array.isArray(dataToProcess[0]) ? dataToProcess[0].map(String) : Object.keys(dataToProcess[0]);
          rows = Array.isArray(dataToProcess[0]) ? dataToProcess.slice(1) : dataToProcess;
        }
      } else if (dataToProcess.table && Array.isArray(dataToProcess.table)) {
        if (dataToProcess.table.length > 0) {
          headers = Array.isArray(dataToProcess.table[0]) ? dataToProcess.table[0].map(String) : Object.keys(dataToProcess.table[0]);
          rows = Array.isArray(dataToProcess.table[0]) ? dataToProcess.table.slice(1) : dataToProcess.table;
        }
      }

      // Apply column selection if any
      const columnsToProcess = selectedColumns.indices.length > 0
        ? selectedColumns.indices.map(index => ({ index, name: headers[index] }))
        : headers.map((name, index) => ({ index, name }));

      // Process each column to create histograms
      return columnsToProcess.map(({ index, name }) => {
        // Extract values for this column
        const values = rows.map(row => {
          const value = Array.isArray(row) ? row[index] : row[name];
          return typeof value === 'string' ? parseFloat(value) : value;
        }).filter(val => val !== null && val !== undefined && !isNaN(val as number)) as number[];

        // Skip if no numeric values
        if (values.length === 0) return null;

        // Calculate histogram bins
        const binCount = 10;
        const min = Math.max(0, Math.min(...values)); // Ensure min is not negative
        const max = Math.max(...values);
        const binWidth = (max - min) / binCount;

        // Initialize bins
        const bins = Array(binCount).fill(0);

        // Count values in each bin
        values.forEach(value => {
          // Skip negative values
          if (value < 0) return;

          const binIndex = Math.min(
            Math.floor((value - min) / binWidth),
            binCount - 1
          );
          bins[binIndex]++;
        });

        // Create histogram data
        return {
          name,
          values: values.filter(v => v >= 0), // Only positive values
          bins: bins.map((count, i) => ({
            x: min + i * binWidth,
            y: count
          })),
          min,
          max
        };
      }).filter(Boolean) as ColumnHistogram[];
    } catch (error) {
      console.error('Error calculating histograms:', error);
      return [];
    }
  }, [data, filteredData, selectedColumns, isLoading, panelFilters]);

  // Configure plot options
  const config = {
    responsive: true,
    displayModeBar: false,
    doubleClick: 'reset',
    displaylogo: false
  };

  // Handle zoom selection events
  const handleZoomSelection = (eventData: any) => {
    if (!onZoomSelection || !eventData) return;

    // Extract the selected range from the event data
    let xRange;
    if (eventData['xaxis.range[0]'] && eventData['xaxis.range[1]']) {
      xRange = [eventData['xaxis.range[0]'], eventData['xaxis.range[1]']];
    } else if (eventData['xaxis.range']) {
      xRange = eventData['xaxis.range'];
    }

    if (!xRange || xRange.length !== 2) return;

    // Get the column name from the active tab
    const activeTabIndex = parseInt(document.querySelector('.ant-tabs-tabpane-active')?.getAttribute('data-node-key') || '0');
    const column = columnHistograms[activeTabIndex]?.name;
    if (!column) return;

    const min = xRange[0];
    const max = xRange[1];

    // Check if this is a datetime column - exact match only (case insensitive)
    const isDateColumn = column === 'DateTime';

    if (isDateColumn) {
      // For date columns, use a fixed column name
      onZoomSelection('date', min, max, 'histogram-panel');
    } else {
      // For regular numeric columns, use the actual column name
      onZoomSelection(column, min, max, 'histogram-panel');
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-full">
        <Spin size="large" tip="Loading histogram data..." />
      </div>
    );
  }

  if (!columnHistograms || columnHistograms.length === 0) {
    return (
      <div className="flex justify-center items-center h-full">
        <Empty description="No histogram data available" />
      </div>
    );
  }

  return (
    <div className="histogram-panel h-full" ref={containerRef}>
      <Tabs defaultActiveKey="0" style={{ height: '100%' }}>
        {columnHistograms.map((column, index) => (
          <Tabs.TabPane tab={column.name} key={index.toString()}>
            <div className="p-4 h-full">
              <Plot
                data={[{
                  type: 'bar',
                  x: column.bins.map(bin => bin.x),
                  y: column.bins.map(bin => bin.y),
                  marker: {
                    color: '#38bdf8',
                    line: {
                      color: 'rgba(0,0,0,0.3)',
                      width: 1
                    }
                  },
                  hoverinfo: 'x+y',
                  hovertemplate: 'Value: %{x}<br>Count: %{y}<extra></extra>'
                }] as any}
                layout={{
                  title: `Histogram: ${column.name}`,
                  autosize: true,
                  height: plotHeight,
                  margin: { l: 50, r: 50, b: 40, t: 40, pad: 4 },
                  xaxis: {
                    title: 'Value',
                    showgrid: true,
                    zeroline: true,
                    range: [0, column.max * 1.05] // Ensure only positive x-axis with some padding
                  },
                  yaxis: {
                    title: 'Count',
                    showgrid: true,
                    zeroline: true
                  },
                  bargap: 0.05,
                  paper_bgcolor: 'rgba(0,0,0,0)',
                  plot_bgcolor: 'rgba(0,0,0,0)'
                } as any}
                config={config as any}
                onRelayout={handleZoomSelection}
                style={{ width: '100%' }}
                useResizeHandler={true}
              />
              <div className="flex justify-between text-sm mt-4">
                <span><strong>Min:</strong> {column.min.toFixed(2)}</span>
                <span><strong>Max:</strong> {column.max.toFixed(2)}</span>
              </div>
            </div>
          </Tabs.TabPane>
        ))}
      </Tabs>
    </div>
  );
};

export default HistogramPanel;
