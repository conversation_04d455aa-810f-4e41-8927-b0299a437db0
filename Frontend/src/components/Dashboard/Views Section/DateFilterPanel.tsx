import React, { useState } from 'react';
import { DatePicker, Button, Space } from 'antd';
import { DateFilter } from './types';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker;

interface DateFilterPanelProps {
  onDateFilterChange: (startDate: string | null, endDate: string | null) => void;
  dateFilter?: DateFilter;
}

const DateFilterPanel: React.FC<DateFilterPanelProps> = ({ 
  onDateFilterChange,
  dateFilter = { startDate: null, endDate: null }
}) => {
  const [dateRange, setDateRange] = useState<[dayjs.Dayjs | null, dayjs.Dayjs | null]>([
    dateFilter.startDate ? dayjs(dateFilter.startDate) : null,
    dateFilter.endDate ? dayjs(dateFilter.endDate) : null
  ]);

  const handleDateChange = (dates: [dayjs.Dayjs | null, dayjs.Dayjs | null] | null) => {
    if (dates) {
      setDateRange(dates);
    } else {
      setDateRange([null, null]);
    }
  };

  const handleApplyFilter = () => {
    const startDate = dateRange[0] ? dateRange[0].format('YYYY-MM-DD') : null;
    const endDate = dateRange[1] ? dateRange[1].format('YYYY-MM-DD') : null;
    onDateFilterChange(startDate, endDate);
  };

  const handleClearFilter = () => {
    setDateRange([null, null]);
    onDateFilterChange(null, null);
  };

  return (
    <div className="date-filter-panel">
      <Space>
        <RangePicker 
          value={dateRange as [dayjs.Dayjs | null, dayjs.Dayjs | null]}
          onChange={handleDateChange}
          format="YYYY-MM-DD"
        />
        <Button type="primary" onClick={handleApplyFilter}>Apply</Button>
        <Button onClick={handleClearFilter}>Clear</Button>
      </Space>
    </div>
  );
};

export default DateFilterPanel;
