import React from 'react';
import { Tag, Tooltip } from 'antd';
import { CloseOutlined } from '@ant-design/icons';
import { PanelFilter } from './FilterTypes';
import { DateFilter, ColumnSelection } from './types';
import { useSelector, useDispatch } from 'react-redux';
import { toggleAnnotationFilter } from '../../../Redux/slices/annotationSlice';
import { toggleOperationFilter, toggleSelectedOperation } from '../../../Redux/slices/operationSlice';

interface AppliedFiltersProps {
  selectedColumns: ColumnSelection;
  dateFilter: DateFilter;
  conditionalFilters: PanelFilter[];
  onRemoveFilter: any;
  onClearAllFilters: () => void;
}

const AppliedFilters: React.FC<AppliedFiltersProps> = ({
  selectedColumns,
  dateFilter,
  conditionalFilters,
  onRemoveFilter,
  onClearAllFilters
}) => {
    // Get annotations from Redux store
    const annotations = useSelector((state: any) => state.annotations);

    // Get operations from Redux store
    const operations = useSelector((state: any) => state.operations);
    // Helper function to format date strings
  const formatDate = (dateString: string | null): string => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  // Helper function to get operator display text
  const getOperatorDisplay = (operator: string): string => {
    switch (operator) {
      case '=': return 'equals';
      case '!=': return 'not equals';
      case '>': return 'greater than';
      case '>=': return 'greater than or equal to';
      case '<': return 'less than';
      case '<=': return 'less than or equal to';
      case 'contains': return 'contains';
      case 'starts-with': return 'starts with';
      case 'ends-with': return 'ends with';
      case 'by_value': return 'is one of';
      default: return operator;
    }
  };

  // Helper function to format filter value for display
  const formatFilterValue = (value: any): string => {
    if (Array.isArray(value)) {
      return value.join(', ');
    } else if (typeof value === 'string' && value.includes(',')) {
      // If it's a comma-separated string, format it nicely
      return value.split(',').map(v => v.trim()).join(', ');
    } else {
      return String(value);
    }
  };

  // Helper function to truncate long values for display
  const truncateValue = (value: string, maxLength: number = 20): string => {
    if (value.length <= maxLength) return value;
    return value.substring(0, maxLength) + '...';
  };

  // Get annotation filters from Redux
  const annotationsState = useSelector((state: any) => state.annotations);
  const annotationFilters = annotationsState?.annotations
    ? annotationsState.annotations.flatMap((group: any) =>
        // First, find the annotations that are being used as filters
        group.annotations
          .filter((annotation: any) => annotation.applyAsFilter)
          .map((annotation: any) => {
            // Use the group's columnName which is the original column where the annotation was created
            // This ensures the column name doesn't change when switching tabs
            return {
              id: annotation.shapeId,
              columnName: group.columnName, // Use the group's columnName instead of the active column
              x0: annotation.x0,
              x1: annotation.x1,
              y0: annotation.y0,
              y1: annotation.y1
            };
          })
      )
    : [];

  // Get operation filters from Redux
  const operationsState = useSelector((state: any) => state.operations);
  const operationFilters = operationsState?.operations
    ? operationsState.operations.flatMap((group: any) =>
        // Find operations that are being used as filters
        group.operations
          .filter((operation: any) => operation.applyAsFilter)
          .map((operation: any) => {
            return {
              id: operation.operationId,
              columnName: group.columnName,
              y0: operation.y0,
              y1: operation.y1
            };
          })
      )
    : [];

  // Get dispatch at the component level
  const dispatch = useDispatch();

  // Function to handle removing an annotation filter
  const handleRemoveAnnotationFilter = (shapeId: string, columnName: string) => {
    // Find the annotation group that contains this annotation
    // This ensures we're using the correct column name when removing the filter
    const group = annotationsState?.annotations?.find(
      (g: any) => g.columnName === columnName
    );

    if (group) {
      // Dispatch the toggleAnnotationFilter action with the correct column name
      const data = {
        columnName: columnName,
        shapeId: shapeId
      };
      dispatch(toggleAnnotationFilter(data));
    } else {
      console.error(`Could not find annotation group for column: ${columnName}`);
    }
  };

  // Function to handle removing an operation filter
  const handleRemoveOperationFilter = (operationId: string, columnName: string) => {
    // Find the operation group that contains this operation
    const group = operationsState?.operations?.find(
      (g: any) => g.columnName === columnName
    );

    if (group) {
      // Find the operation object to pass to toggleSelectedOperation
      const operation = group.operations.find((op: any) => op.operationId === operationId);

      // Dispatch the toggleOperationFilter action with the correct column name
      const data = {
        columnName: columnName,
        operationId: operationId
      };
      dispatch(toggleOperationFilter(data));

      // When removing a filter, we want to make the operation visible in the chart
      // So we need to check the checkbox if it's not already checked
      if (operation) {
        const selectedGroup = operations?.selectedOperations?.find((group: any) =>
          group.columnName === columnName
        );

        const isAlreadySelected = selectedGroup && selectedGroup.operations.some((selOp: any) =>
          selOp.operationId === operationId
        );

        // If not already selected, select it to make it visible in the chart
        if (!isAlreadySelected) {
          dispatch(toggleSelectedOperation({
            columnName: columnName,
            operation: operation
          }));
        }
      }
    } else {
      console.error(`Could not find operation group for column: ${columnName}`);
    }
  };

  return (
    <div className="applied-filters mb-4">
      <div className="flex items-center mb-2">
        <span className="text-gray-700 font-medium mr-2">Applied Filters:</span>
      </div>
      <div className="flex flex-wrap gap-2">
        {/* Date Filter */}
        {(dateFilter.startDate || dateFilter.endDate) && (
          <Tag
            color="blue"
            closable
            onClose={() => onRemoveFilter('date-filter', 'global')}
            closeIcon={<CloseOutlined />}
          >
            <Tooltip title="Date Range Filter">
              <span>
                Date: {formatDate(dateFilter.startDate)} - {formatDate(dateFilter.endDate)}
              </span>
            </Tooltip>
          </Tag>
        )}

        {/* Column Selection Filter */}
        {selectedColumns.headers.length > 0 && (
          <Tag
            color="blue"
            closable
            onClose={() => onRemoveFilter('column-filter', 'global')}
            closeIcon={<CloseOutlined />}
          >
            <Tooltip title={`Selected Columns: ${selectedColumns.headers.join(', ')}`}>
              <span>
                Columns: {selectedColumns.headers.length} selected
              </span>
            </Tooltip>
          </Tag>
        )}

        {/* Conditional Filters */}
        {conditionalFilters.map((filter: any) => {
          const formattedValue = formatFilterValue(filter.value);
          const displayValue = truncateValue(formattedValue);

          return (
            <Tag
              key={filter.id}
              color="blue"
              closable
              onClose={() => onRemoveFilter(filter.id, 'global')}
              closeIcon={<CloseOutlined />}
            >
              <Tooltip title={`${filter.column} ${getOperatorDisplay(filter.operator)} ${formattedValue}`}>
                <span>
                  {filter.column} {getOperatorDisplay(filter.operator)} {displayValue}
                </span>
              </Tooltip>
            </Tag>
          );
        })}

        {/* Annotation Filters */}
        {annotationFilters.map((filter: any) => (
          <Tag
            key={filter.id}
            color="purple" // Different color for annotation filters
            closable
            onClose={() => handleRemoveAnnotationFilter(filter.id, filter.columnName)}
            closeIcon={<CloseOutlined />}
          >
            <Tooltip title={`Annotation on ${filter.columnName}: ${formatDate(filter.x0)} to ${formatDate(filter.x1)}, ${filter.y0} to ${filter.y1}`}>
              <span>
                Annotation: {filter.columnName} {truncateValue(formatDate(filter.x0))} - {truncateValue(formatDate(filter.x1))}
              </span>
            </Tooltip>
          </Tag>
        ))}

        {/* Operation Filters */}
        {operationFilters.map((filter: any) => (
          <Tag
            key={filter.id}
            color="green" // Different color for operation filters (green)
            closable
            onClose={() => handleRemoveOperationFilter(filter.id, filter.columnName)}
            closeIcon={<CloseOutlined />}
          >
            <Tooltip title={`Operation on ${filter.columnName}: ${filter.y0.toFixed(2)} to ${filter.y1.toFixed(2)}`}>
              <span>
                Operation: {filter.columnName} {filter.y0.toFixed(2)} - {filter.y1.toFixed(2)}
              </span>
            </Tooltip>
          </Tag>
        ))}

        {/* Show "No filters applied" if no filters are active */}
        {!dateFilter.startDate && !dateFilter.endDate &&
         selectedColumns.headers.length === 0 &&
         conditionalFilters.length === 0 &&
         annotationFilters.length === 0 &&
         operationFilters.length === 0 && (
          <span className="text-gray-500">No filters applied</span>
        )}
      </div>
    </div>
  );
};

export default AppliedFilters;
