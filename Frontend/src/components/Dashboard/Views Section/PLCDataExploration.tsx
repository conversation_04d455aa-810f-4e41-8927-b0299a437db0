import React from 'react';
import { Card, Typography, Space, Button } from 'antd';
import { ExperimentOutlined, SettingOutlined, DatabaseOutlined } from '@ant-design/icons';

const { Title, Paragraph } = Typography;

interface PLCDataExplorationProps {
  // Future props for PLC data exploration
}

const PLCDataExploration: React.FC<PLCDataExplorationProps> = () => {
  return (
    <div className="plc-data-exploration h-full flex items-center justify-center bg-gray-50">
      <Card 
        className="max-w-2xl w-full mx-4"
        style={{ textAlign: 'center' }}
      >
        <Space direction="vertical" size="large" className="w-full">
          <div>
            <ExperimentOutlined 
              style={{ 
                fontSize: '64px', 
                color: '#1890ff',
                marginBottom: '16px'
              }} 
            />
            <Title level={2} className="!mb-2">
              PLC Data Exploration
            </Title>
            <Paragraph className="text-gray-600 text-lg">
              Advanced PLC data analysis and visualization platform
            </Paragraph>
          </div>

          <div className="bg-blue-50 p-6 rounded-lg">
            <Title level={4} className="!mb-4">
              Coming Soon
            </Title>
            <Paragraph className="text-gray-700">
              This section will provide comprehensive PLC data exploration capabilities including:
            </Paragraph>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-4">
              <div className="text-center">
                <DatabaseOutlined className="text-2xl text-blue-500 mb-2" />
                <Paragraph className="font-medium">Real-time Data</Paragraph>
                <Paragraph className="text-sm text-gray-600">
                  Live PLC data streaming and monitoring
                </Paragraph>
              </div>
              
              <div className="text-center">
                <SettingOutlined className="text-2xl text-green-500 mb-2" />
                <Paragraph className="font-medium">Process Control</Paragraph>
                <Paragraph className="text-sm text-gray-600">
                  Advanced process control analytics
                </Paragraph>
              </div>
              
              <div className="text-center">
                <ExperimentOutlined className="text-2xl text-purple-500 mb-2" />
                <Paragraph className="font-medium">Comparison Tools</Paragraph>
                <Paragraph className="text-sm text-gray-600">
                  Compare PLC data across different time periods
                </Paragraph>
              </div>
            </div>
          </div>

          <div className="pt-4">
            <Paragraph className="text-gray-500 text-sm">
              This feature is currently under development. 
              <br />
              Please check back for updates or contact support for more information.
            </Paragraph>
          </div>
        </Space>
      </Card>
    </div>
  );
};

export default PLCDataExploration;
