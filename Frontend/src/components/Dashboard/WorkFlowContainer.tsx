import { Flex, message } from "antd";
import React, { useState, useEffect, useCallback } from "react";
import data from "../../img/data.svg";
import insight from "../../img/insight.svg";
import arrowleft from "../../img/arrow-left.svg";

import ParallelTimeSeriesChart from "../charts/ParallelTimeSeriesChart";
import DataTable from "../tables/DataTable";
import ReactFlow, { Node } from "reactflow";
import "reactflow/dist/style.css";
import FlowCanvas from "./FlowCanvas";
import { Table } from "antd";
import type { ColumnsType } from "antd/es/table";
import QueryBuilder from "./QueryBuilder";
import OperationConfig from "./OperationConfig";
import file from "../../img/file.svg";
import box from "../../img/box.svg";
import { getRequest } from "../../utils/apiHandler";
import Notiflix from "notiflix";
import InsightTabContent from './Insight Section/InsightTabContent';
import { useLocation, useNavigate } from 'react-router-dom';  // Import useLocation from React Router
import { Histogram } from "../charts/Histogram";
import { Menu } from 'antd';
import { useAuth } from '../../context/AuthContext';
import DataTab from "./Data Section/DataTab";
import InsightContent from "./Insight Section/InsightTab";
import InsightTab from "./Insight Section/InsightTab";
import AlertTab from "./Alert Section/AlertTab";
import AlertTabContent from "./Alert Section/AlertTabContent";
import DataTabContent from "./Data Section/DataTabContent";
import FolderStructure from "./FolderStructure";
import "./Views Section/ViewStyles.css";
import ViewSidebar from "./Views Section/ViewSidebar";
import { ComponentType, ColumnSelection, DateFilter } from './Views Section/types';
import { PanelFilter, createColumnFilter, createDateFilter, createValueRangeFilter, ValueRangeFilter, updateFilter } from './Views Section/FilterTypes';
import { useSelector } from "react-redux";
import ViewContent from "./Views Section/ViewContent";
import { useDispatch } from 'react-redux';
import { addTab } from "../../Redux/slices/viewTabSlice";
import { toggleAnnotationFilter } from "../../Redux/slices/annotationSlice";
import { toggleOperationFilter, toggleSelectedOperation } from "../../Redux/slices/operationSlice";
import { hideFullscreenLoader, showFullscreenLoader } from "../Common/CommonLoader";
interface FileData {
  csv_id: string;
  file_name: string;
  file_path: string;
  version: number;
  created_at?: string;
  updated_at?: string;
  data?: any;
}

function WorkbookContainer() {
  const [activeTab, setActiveTab] = useState("tab1");
  const [collapse, setCollapse] = useState(false);
  const [uploadedFiles, setUploadedFiles] = useState<FileData[]>([]);
  const [originalChartData, setOriginalChartData] = useState<any>({
    timeSeries: { categories: [], series: [] },
  });
  const [filteredChartData, setFilteredChartData] =
    useState<any>(originalChartData);
  const [originalFileData, setOriginalFileData] = useState<any>([]);
  const [filteredFileData, setFilteredFileData] = useState<any[]>([]);
  const [showVisualisation, setShowVisualization] = useState<Boolean>(true);
  const [dataContentSection, setDataContentSection] = useState<"file-listing" | "data-visualisation" | "golden-parameters" | "golden-parameters-details" | "create-parameters">("file-listing");
  const [nodes, setNodes] = useState<Node[]>([]);
  const [fileQueries, setFileQueries] = useState<Record<string, any>>({});
  const [currentFile, setCurrentFile] = useState<string | null>(null);
  const [showQueryBuilder, setShowQueryBuilder] = useState(false);
  const [showOperationConfig, setShowOperationConfig] = useState(false);
  const [queries, setQueries] = useState({});
  const [nodeConfigurations, setNodeConfigurations] = useState<Record<string, any>>({});
  const [workbookId, setWorkbookId] = useState(1);
  const [activeDashboard, setActiveDashboard] = useState<string>("");

  // Dynamic SubPlot Data & Layout State
  const [dynamicPlotData, setDynamicPlotData] = useState<any>([]);
  const [dynamicPlotLayout, setDynamicPlotLayout] = useState<any>([]);
  const [flowType , setFlowType] = useState<any>('workflow')
  // const [selectSystems,setSelectSystems] = useState<any>([]);
  // const [reloadSelectSystems,setReloadSelectSystems] = useState<boolean>(true);
  const location = useLocation();
  const navigate = useNavigate();
  const { authState } = useAuth();
  const dispatch = useDispatch();
  //States for views Section
  const [files, setFiles] = useState<FileData[]>([]);
  const [selectedFile, setSelectedFile] = useState<FileData | null>(null);
  const [fileSelected, setFileSelected] = useState(false);
  const [initialPanelsCreated, setInitialPanelsCreated] = useState(false);
  const [activePanels, setActivePanels] = useState<ComponentType[]>([]);
  const [loadingFiles, setLoadingFiles] = useState<boolean>(true);

  // State for selections and filters
  const [selectedColumns, setSelectedColumns] = useState<ColumnSelection>({ indices: [], headers: [] });
  const [dateFilter, setDateFilter] = useState<DateFilter>({ startDate: null, endDate: null });

  // State for conditional column filters (from Handsontable)
  const [conditionalFilters, setConditionalFilters] = useState<PanelFilter[]>([]);

  // New filter state system
  const [panelFilters, setPanelFilters] = useState<Record<string, PanelFilter[]>>({});

  // Initialize panel filters for data table and overview panels
  useEffect(() => {
    setPanelFilters({
      'data-table-panel': [],
      'overview-panel': [],
      'histogram-panel': [],
      'time-series-panel': []
    });
  }, []);
  const auth = useAuth();
  const selectSystems = useSelector((state: any) => state.systems.systems);

  const [viewTabOpen , setViewTabOpen] = useState(false);
  console.log('viewTabOpen :', viewTabOpen);
  //Functions for views section

  // Function to reset all view-related states
  const resetViewStates = () => {
    console.log('Resetting view states');
    // setSelectedFile(null);
    // setFileSelected(false);
    // setInitialPanelsCreated(false);
    // setActivePanels([]);
    // setSelectedRows({ indices: [], data: [] });
    // setSelectedColumns({ indices: [], headers: [] });
    // setDateFilter({ startDate: null, endDate: null });
    // setViewStructure(null);
    // setFlowType('view');
    // setViewTabOpen(true)
    // showFolderComponent = true

    // Navigate back to workflow folders
    // navigate('/?tab=insight&viewId=0');
  };

  // Handle file selection
  const handleFileSelect = async (file: FileData) => {
    try {
      // Fetch file data if not already loaded
      // Notiflix.Loading.circle('Loading...');
      showFullscreenLoader();
      if (!file.data) {
        const response = await getRequest(`/file/${file.csv_id}`);
        if (response.data.status === 200) {
          file.data = response.data.data;
        }
      }

      setSelectedFile(file);
      setFileSelected(true);
      setInitialPanelsCreated(false); // Reset when file changes

      // Reset selections and filters when file changes
      setSelectedColumns({ indices: [], headers: [] });
      setDateFilter({ startDate: null, endDate: null });
      // Notiflix.Loading.remove();
      hideFullscreenLoader();
    } catch (error) {
      console.error('Error fetching file data:', error);
      message.error('Failed to fetch file data');
      // Notiflix.Notify.failure('Failed to fetch file data');
      // Notiflix.Loading.remove();
      hideFullscreenLoader();
    }
  };

    // Fetch files when component mounts or when selectSystems changes
      useEffect(() => {
        fetchUploadedFiles();
      }, [selectSystems]);


      const fetchUploadedFiles = async () => {
        try {
          setLoadingFiles(true);
          const systemIdsString = selectSystems.length ? selectSystems[0].systems
            ?.map((system: { systemId: number }) => {
              return system.systemId;
            })
            .join(",") : '';
          const apiUrl = systemIdsString ? `/file?systems_id=${systemIdsString}` : '/file';
          const response = await getRequest(apiUrl);

          if (response.data && response.data.data.files) {
            setFiles(response.data.data.files);
            setLoadingFiles(false);
          }
        } catch (error: any) {
          console.error('Error fetching uploaded files:', error);
          if (error.status === 403) {
            auth.logout();
            // Notiflix.Notify.failure(error.response.data.message);
            message.error(error.response.data.message);
            return;
          }
          // Notiflix.Notify.failure('Failed to fetch uploaded files');
          message.error('Failed to fetch uploaded files');
          setLoadingFiles(false);
        }
      };

      // Handler for column selection
      const handleColumnSelection = (indices: number[], headers: string[]) => {
        setSelectedColumns({ indices, headers });

        // Update filter state with new column selection
        setPanelFilters(prevFilters => {
          const newFilters = { ...prevFilters };
          Object.keys(newFilters).forEach(panelId => {
            // Skip updating the OverviewPanel's own filter state
            if (panelId === 'overview-panel') return;

            const panelFilterList = [...(newFilters[panelId] || [])];
            const columnFilter = panelFilterList.find(f => f.type === 'column');

            if (columnFilter) {
              // Update existing filter
              const updatedFilter = {
                ...columnFilter,
                selection: { indices, headers }
              };
              newFilters[panelId] = updateFilter(panelFilterList, updatedFilter);
            } else if (indices.length > 0) {
              // Add new filter
              newFilters[panelId] = [
                ...panelFilterList,
                createColumnFilter('column-filter', { indices, headers })
              ];
            }
          });
          return newFilters;
        });
      };

      // Handler for date filter
      const handleDateFilter = (startDate: string | null, endDate: string | null) => {
        setDateFilter({ startDate, endDate });

        // Update filter state with new date filter
        setPanelFilters(prevFilters => {
          const newFilters = { ...prevFilters };
          Object.keys(newFilters).forEach(panelId => {
            const panelFilterList = [...(newFilters[panelId] || [])];
            const dateRangeFilter = panelFilterList.find(f => f.type === 'date');

            if (dateRangeFilter) {
              // Update existing filter
              const updatedFilter = {
                ...dateRangeFilter,
                startDate,
                endDate
              };
              newFilters[panelId] = updateFilter(panelFilterList, updatedFilter);
            } else if (startDate && endDate) {
              // Add new filter
              newFilters[panelId] = [
                ...panelFilterList,
                createDateFilter('date-filter', startDate, endDate)
              ];
            }
          });
          return newFilters;
        });
      };

      // Handler for zoom selection in charts
      const handleValueRangeFilter = (column: string, min: any, max: any, sourcePanelId?: string , zoomData?:any) => {
        // If zoomData is provided with x1 and x2 (date range), update the dateFilter
        if (zoomData && zoomData.x1 && zoomData.x2) {
          handleDateFilter(zoomData.x1, zoomData.x2);
        }

        // if(selectedFile?.data){
        //   const initialFiltered = getFilteredData(selectedFile.data ,column);
        //   console.log('initialFiltered 222222222222222222222222222222222222222:', initialFiltered);
        //   if(initialFiltered)
        //   setFilteredData(nitialFiltered);
        // }
        // Check if we're dealing with date strings
        // if (typeof min === 'string' && min.includes('-') && typeof max === 'string' && max.includes('-')) {
        //   // Use date filter mechanism for date ranges
        //   const startDate = min;
        //   const endDate = max;

        //   // Update the date filter state
        //   setDateFilter({ startDate, endDate });

        //   // Update filter state with new date filter
        //   setPanelFilters(prevFilters => {
        //     const newFilters = { ...prevFilters };
        //     Object.keys(newFilters).forEach(panelId => {
        //       // Skip the source panel to avoid circular filtering
        //       const sourceId = sourcePanelId || 'unknown-source';
        //       if (panelId === sourceId ||
        //           (sourcePanelId === 'time-series-panel' && panelId === 'time-series-panel') ||
        //           (sourcePanelId === 'histogram-panel' && panelId === 'histogram-panel')) {
        //         return;
        //       }

        //       const panelFilterList = [...(newFilters[panelId] || [])];
        //       const dateRangeFilter = panelFilterList.find(f => f.type === 'date');

        //       if (dateRangeFilter) {
        //         // Update existing filter
        //         const updatedFilter = {
        //           ...dateRangeFilter,
        //           startDate,
        //           endDate
        //         };
        //         newFilters[panelId] = updateFilter(panelFilterList, updatedFilter);
        //       } else {
        //         // Add new filter
        //         newFilters[panelId] = [
        //           ...panelFilterList,
        //           createDateFilter('date-filter', startDate, endDate)
        //         ];
        //       }
        //     });
        //     return newFilters;
        //   });

        //   return; // Exit early since we've handled the date filter case
        // }

        // // For numeric columns
        // setPanelFilters(prevFilters => {
        //   const newFilters = { ...prevFilters };
        //   const filterId = `value-range-${column}`;

        //   // Apply filter to all panels
        //   Object.keys(newFilters).forEach(panelId => {
        //     // Skip the source panel
        //     const sourceId = sourcePanelId || 'unknown-source';
        //     if (panelId === sourceId ||
        //         (sourcePanelId === 'time-series-panel' && panelId === 'time-series-panel') ||
        //         (sourcePanelId === 'histogram-panel' && panelId === 'histogram-panel')) {
        //       return;
        //     }

        //     const panelFilterList = [...(newFilters[panelId] || [])];

        //     // Find existing filter for this column
        //     const valueRangeFilter = panelFilterList.find(f =>
        //       f.type === 'value-range' && (f as ValueRangeFilter).column === column
        //     );

        //     if (valueRangeFilter) {
        //       // Update existing filter
        //       const updatedFilter = {
        //         ...valueRangeFilter,
        //         min,
        //         max
        //       };
        //       newFilters[panelId] = updateFilter(panelFilterList, updatedFilter);
        //     } else {
        //       // Add new filter
        //       newFilters[panelId] = [
        //         ...panelFilterList,
        //         createValueRangeFilter(filterId, column, min, max)
        //       ];
        //     }
        //   });

        //   return newFilters;
        // });
      };

      // Register a panel in the filter system
      const registerPanelFilters = (panelId: string, initialFilters: PanelFilter[] = []) => {
        setPanelFilters(prevFilters => {
          if (prevFilters[panelId]) return prevFilters;
          return {
            ...prevFilters,
            [panelId]: initialFilters
          };
        });

        // Return the current filters for this panel
        return panelFilters[panelId] || [];
      };

      // Add a filter to a specific panel
      const handleAddFilter = (filter: PanelFilter, panelId: string) => {
        console.log(`Adding filter to panel ${panelId}:`, filter);

        setPanelFilters(prevFilters => {
          const panelFilters = prevFilters[panelId] || [];

          // Check if this filter already exists (by id)
          const existingFilterIndex = panelFilters.findIndex(f => f.id === filter.id);

          if (existingFilterIndex >= 0) {
            // Update existing filter
            const updatedPanelFilters = [...panelFilters];
            updatedPanelFilters[existingFilterIndex] = filter;

            return {
              ...prevFilters,
              [panelId]: updatedPanelFilters
            };
          } else {
            // Add new filter
            return {
              ...prevFilters,
              [panelId]: [...panelFilters, filter]
            };
          }
        });
      };

      // Remove a filter from a specific panel or all panels if it's a conditional filter
      const handleRemoveFilter = (filterId: string, panelId: string) => {
        // If panelId is 'global', this is a global filter
        if (panelId === 'global') {
          // Handle different filter types
          if (filterId === 'date-filter') {
            // Clear date filter
            setDateFilter({ startDate: null, endDate: null });
          } else if (filterId === 'column-filter') {
            // Clear column selection
            setSelectedColumns({ indices: [], headers: [] });
          } else {
            // Remove a specific conditional filter
            setConditionalFilters(prevFilters =>
              prevFilters.filter(filter => filter.id !== filterId)
            );
          }
        } else {
          setConditionalFilters([]);
          // Handle panel-specific filters
          setPanelFilters(prevFilters => {
            const panelFilters = prevFilters[panelId] || [];
            return {
              ...prevFilters,
              [panelId]: panelFilters.filter(filter => filter.id !== filterId)
            };
          });
        }
      };

      // Handler for conditional column filters (from Handsontable)
      const handleConditionalFilter = (filter: PanelFilter) => {
        // Update the conditionalFilters state
        setConditionalFilters(prevFilters => {
          // Check if this filter already exists (by column and operator)
          const existingFilterIndex = prevFilters.findIndex(f =>
            f.type === 'conditional-column' &&
            (f as any).column === (filter as any).column &&
            (f as any).operator === (filter as any).operator
          );

          if (existingFilterIndex >= 0) {
            // Update existing filter
            const updatedFilters = [...prevFilters];
            updatedFilters[existingFilterIndex] = filter;
            return updatedFilters;
          } else {
            // Add new filter
            return [...prevFilters, filter];
          }
        });
      };

      // Clear all filters
      const handleClearAllFilters = () => {
        // Clear filter states
        setSelectedColumns({ indices: [], headers: [] });
        setDateFilter({ startDate: null, endDate: null });

        // Clear conditional filters
        setConditionalFilters([]);

        // Clear panel filters
        setPanelFilters(prevFilters => {
          const newFilters = { ...prevFilters };
          Object.keys(newFilters).forEach(panelId => {
            newFilters[panelId] = [];
          });
          return newFilters;
        });

        // Clear annotation filters by setting applyAsFilter to false for all annotations
        if (annotationsState && annotationsState.annotations) {
          annotationsState.annotations.forEach((group: { columnName: string, annotations: any[] }) => {
            group.annotations.forEach((annotation: { applyAsFilter?: boolean, shapeId: string }) => {
              if (annotation.applyAsFilter) {
                // Use the toggleAnnotationFilter action to set applyAsFilter to false
                dispatch(toggleAnnotationFilter({
                  columnName: group.columnName,
                  shapeId: annotation.shapeId
                }));
              }
            });
          });
        }

        // Clear operation filters by setting applyAsFilter to false for all operations
        if (operationsState && operationsState.operations) {
          operationsState.operations.forEach((group: { columnName: string, operations: any[] }) => {
            group.operations.forEach((operation: { applyAsFilter?: boolean, operationId: string }) => {
              if (operation.applyAsFilter) {
                // Use the toggleOperationFilter action to set applyAsFilter to false
                dispatch(toggleOperationFilter({
                  columnName: group.columnName,
                  operationId: operation.operationId
                }));

                // Find the full operation object from the operations state
                const fullOperation = group.operations.find((op: any) => op.operationId === operation.operationId);

                // When clearing filters, we want to make operations visible in the chart
                // So we need to check the checkbox if it's not already checked
                if (fullOperation) {
                  const selectedGroup = operationsState.selectedOperations.find(
                    (selGroup: any) => selGroup.columnName === group.columnName
                  );

                  const isAlreadySelected = selectedGroup && selectedGroup.operations.some((op: any) =>
                    op.operationId === operation.operationId
                  );

                  // If not already selected, select it to make it visible in the chart
                  if (!isAlreadySelected) {
                    dispatch(toggleSelectedOperation({
                      columnName: group.columnName,
                      operation: fullOperation
                    }));
                  }
                }
              }
            });
          });
        }

        console.log('All filters cleared');
      };

      // Get annotations from Redux store at the component level
      const annotationsState = useSelector((state: any) => state.annotations);

      // Get operations from Redux store at the component level
      const operationsState = useSelector((state: any) => state.operations);

      // Centralized filtering function
      const getFilteredData = (data: any, zoomData? :any) => {
        if (!data) return null;

        let filteredData = [...data]; // Create a copy of the original data

        // Apply date filter - use zoomData if available, otherwise use dateFilter
        const useZoomData = zoomData && zoomData.x1 && zoomData.x2;
        const startDateStr = useZoomData ? zoomData.x1 : dateFilter.startDate;
        const endDateStr = useZoomData ? zoomData.x2 : dateFilter.endDate;

        if (startDateStr && endDateStr) {
          // Find date column
          const headers = Array.isArray(data[0]) ? data[0] : Object.keys(data[0]);
          const dateColumnIndex = headers.findIndex(header =>
            header.toLowerCase() === 'datetime'
          );

          if (dateColumnIndex >= 0) {
            const startDate = new Date(startDateStr);
            const endDate = new Date(endDateStr);

            filteredData = filteredData.filter(row => {
              const dateValue = Array.isArray(row) ?
                row[dateColumnIndex] :
                row[headers[dateColumnIndex]];

              if (!dateValue) return false;

              const rowDate = new Date(dateValue);
              return rowDate >= startDate && rowDate <= endDate;
            });
          }
        }

        if (conditionalFilters.length > 0) {
          // Check if we have any special 'filtered' filters from Handsontable
          const hasFilteredValue = conditionalFilters.some(filter =>
            (filter as any).value === 'filtered' && (filter as any).operator === '='
          );

          // If we have a 'filtered' value, we're using Handsontable's built-in filtering
          // The filtered data is already passed to us, so we don't need to do anything
          if (!hasFilteredValue) {
            // Only apply our own filtering if we don't have a 'filtered' value
            const headers = Array.isArray(data[0]) ? data[0] : Object.keys(data[0]);

            // Apply each conditional filter
            conditionalFilters.forEach(filter => {
              const conditionalFilter = filter as any; // Type assertion
              const column = conditionalFilter.column;
              const operator = conditionalFilter.operator;
              const filterValue = conditionalFilter.value;

            // Find column index
            const columnIndex = headers.findIndex(h => h === column);

            if (columnIndex >= 0) {
              filteredData = filteredData.filter(row => {
                const cellValue = Array.isArray(row) ?
                  row[columnIndex] :
                  row[column];

                // Skip null or undefined values
                if (cellValue === null || cellValue === undefined) return false;

                // Convert to appropriate type for comparison
                let typedCellValue: any = cellValue;
                let typedFilterValue: any = filterValue;

                // If both are numeric, convert to numbers
                if (!isNaN(Number(cellValue)) && !isNaN(Number(filterValue))) {
                  typedCellValue = Number(cellValue);
                  typedFilterValue = Number(filterValue);
                } else {
                  // Otherwise, convert both to strings for string operations
                  typedCellValue = String(cellValue);
                  typedFilterValue = String(filterValue);
                }

                // Apply the appropriate comparison based on the operator
                switch (operator) {
                  case '=':
                    return typedCellValue === typedFilterValue;
                  case '!=':
                    return typedCellValue !== typedFilterValue;
                  case '>':
                    return typedCellValue > typedFilterValue;
                  case '>=':
                    return typedCellValue >= typedFilterValue;
                  case '<':
                    return typedCellValue < typedFilterValue;
                  case '<=':
                    return typedCellValue <= typedFilterValue;
                  case 'between':
                    // Handle between operator for numeric values
                    // First check if typedFilterValue is a comma-separated string
                    if (typeof typedFilterValue === 'string' && typedFilterValue.includes(',')) {
                      // Split the string and convert to numbers
                      const [minStr, maxStr] = typedFilterValue.split(',').map(val => val.trim());
                      const min = !isNaN(Number(minStr)) ? Number(minStr) : minStr;
                      const max = !isNaN(Number(maxStr)) ? Number(maxStr) : maxStr;
                      
                      // Convert cell value to number if it's a numeric string
                      const numericCellValue = typeof typedCellValue === 'string' && !isNaN(Number(typedCellValue))
                        ? Number(typedCellValue)
                        : typedCellValue;
                      
                      // Check if value is between min and max (inclusive)
                      return numericCellValue >= min && numericCellValue <= max;
                    }
                    // Then check if it's an array
                    else if (Array.isArray(typedFilterValue) && typedFilterValue.length === 2) {
                      const [min, max] = typedFilterValue.map(val =>
                        typeof val === 'string' && !isNaN(Number(val)) ? Number(val) : val
                      );
                      
                      // Convert cell value to number if it's a numeric string
                      const numericCellValue = typeof typedCellValue === 'string' && !isNaN(Number(typedCellValue))
                        ? Number(typedCellValue)
                        : typedCellValue;
                      
                      // Check if value is between min and max (inclusive)
                      return numericCellValue >= min && numericCellValue <= max;
                    }
                    return false;
                  case 'not_between':
                    // Handle not_between operator for numeric values
                    // First check if typedFilterValue is a comma-separated string
                    if (typeof typedFilterValue === 'string' && typedFilterValue.includes(',')) {
                      // Split the string and convert to numbers
                      const [minStr, maxStr] = typedFilterValue.split(',').map(val => val.trim());
                      const min = !isNaN(Number(minStr)) ? Number(minStr) : minStr;
                      const max = !isNaN(Number(maxStr)) ? Number(maxStr) : maxStr;
                      
                      // Convert cell value to number if it's a numeric string
                      const numericCellValue = typeof typedCellValue === 'string' && !isNaN(Number(typedCellValue))
                        ? Number(typedCellValue)
                        : typedCellValue;
                      
                      // Check if value is outside min and max
                      return numericCellValue < min || numericCellValue > max;
                    }
                    // Then check if it's an array
                    else if (Array.isArray(typedFilterValue) && typedFilterValue.length === 2) {
                      const [min, max] = typedFilterValue.map(val =>
                        typeof val === 'string' && !isNaN(Number(val)) ? Number(val) : val
                      );
                      
                      // Convert cell value to number if it's a numeric string
                      const numericCellValue = typeof typedCellValue === 'string' && !isNaN(Number(typedCellValue))
                        ? Number(typedCellValue)
                        : typedCellValue;
                      
                      // Check if value is outside min and max
                      return numericCellValue < min || numericCellValue > max;
                    }
                    return false;
                  case 'contains':
                    return String(typedCellValue).toLowerCase().includes(String(typedFilterValue).toLowerCase());
                  case 'starts-with':
                    return String(typedCellValue).toLowerCase().startsWith(String(typedFilterValue).toLowerCase());
                  case 'ends-with':
                    return String(typedCellValue).toLowerCase().endsWith(String(typedFilterValue).toLowerCase());
                  case 'by_value':
                    // Handle different formats of filter values
                    let filterValues = typedFilterValue;

                    // Handle case where typedFilterValue is a comma-separated string like "1,2,3"
                    if (typeof typedFilterValue === 'string' && typedFilterValue.includes(',')) {
                      filterValues = typedFilterValue.split(',').map(val => val.trim());
                    } else if (!Array.isArray(typedFilterValue)) {
                      // If it's not an array and not a comma-separated string, make it a single-item array
                      filterValues = [typedFilterValue];
                    }

                    // Convert cell value to string for comparison
                    const cellValueStr = String(typedCellValue).toLowerCase();

                    // Check if any value in the array matches the cell value
                    return filterValues.some((val: any) =>
                      String(val).toLowerCase() === cellValueStr
                    );
                  default:
                    return true; // Unknown operator, don't filter
                }
              });
            }
          });
          }
        }

        // Apply annotation filters from the annotationsState passed as a parameter
        if (annotationsState && annotationsState.annotations) {
          // Find annotations that have applyAsFilter set to true
          const annotationsToFilter = annotationsState.annotations.flatMap((group: any) =>
            group.annotations.filter((annotation: any) => annotation.applyAsFilter)
          );

          if (annotationsToFilter.length > 0) {
            console.log('Applying annotation filters:', annotationsToFilter);

            // Find date column for x-axis filtering
            const headers = Array.isArray(data[0]) ? data[0] : Object.keys(data[0]);
            const dateColumnIndex = headers.findIndex(header =>
              header.toLowerCase() === 'datetime'
            );

            // Apply each annotation filter
            annotationsToFilter.forEach((annotation: any) => {
              const columnName = annotation.columnName || annotationsState.activeColumnName;

              // Find the column index for the y-axis values
              const columnIndex = headers.findIndex(header => header === columnName);

              if (dateColumnIndex >= 0 && columnIndex >= 0) {
                // Convert annotation boundaries to proper types
                const x0Date = new Date(annotation.x0);
                const x1Date = new Date(annotation.x1);
                const y0Value = annotation.y0;
                const y1Value = annotation.y1;

                // Filter out data points that fall within the annotation boundaries
                filteredData = filteredData.filter(row => {
                  const dateValue = Array.isArray(row) ?
                    row[dateColumnIndex] :
                    row[headers[dateColumnIndex]];

                  const yValue = Array.isArray(row) ?
                    parseFloat(row[columnIndex]) :
                    parseFloat(row[columnName]);

                  if (!dateValue || isNaN(yValue)) return true; // Keep rows with invalid data

                  const rowDate = new Date(dateValue);

                  // Check if the point falls within the annotation boundaries
                  // For date range, check if the point is between x0 and x1
                  const isInXRange = rowDate >= x0Date && rowDate <= x1Date;

                  // For value range, check if the point is between the min and max values
                  // Note: We need to handle the case where y0 might be greater than y1
                  const minY = Math.min(y0Value, y1Value);
                  const maxY = Math.max(y0Value, y1Value);
                  const isInYRange = yValue >= minY && yValue <= maxY;

                  // Return true to keep points that are OUTSIDE the annotation area
                  return !(isInXRange && isInYRange);
                });
              }
            });
          }
        }

        // Apply operation filters from the operationsState
        if (operationsState && operationsState.operations) {
          // Find operations that have applyAsFilter set to true
          const operationsToFilter = operationsState.operations.flatMap((group: any) =>
            group.operations.filter((operation: any) => operation.applyAsFilter)
          );

          if (operationsToFilter.length > 0) {
            console.log('Applying operation filters:', operationsToFilter);

            // Find headers for column filtering
            const headers = Array.isArray(data[0]) ? data[0] : Object.keys(data[0]);

            // Group operations by column name
            const operationsByColumn: Record<string, any[]> = {};
            operationsToFilter.forEach((operation: any) => {
              const columnName = operation.columnName || operationsState.activeColumnName;
              if (!operationsByColumn[columnName]) {
                operationsByColumn[columnName] = [];
              }
              operationsByColumn[columnName].push(operation);
            });

            // Apply operation filters for each column separately
            Object.entries(operationsByColumn).forEach(([columnName, operations]) => {
              // Find the column index for the y-axis values
              const columnIndex = headers.findIndex(header => header === columnName);

              if (columnIndex >= 0) {
                // Apply each operation filter for this column
                operations.forEach((operation: any) => {
                  // Get the operation boundaries
                  const y0Value = operation.y0;
                  const y1Value = operation.y1;

                  // Get min and max values (in case y0 > y1)
                  const minY = Math.min(y0Value, y1Value);
                  const maxY = Math.max(y0Value, y1Value);

                  console.log(`Applying operation filter for column ${columnName} with range: ${minY} - ${maxY}`);

                  // Filter data points - KEEP only points that are WITHIN the operation range
                  // This is the opposite of annotation filtering
                  filteredData = filteredData.filter(row => {
                    const yValue = Array.isArray(row) ?
                      parseFloat(row[columnIndex]) :
                      parseFloat(row[columnName]);

                    if (isNaN(yValue)) return false; // Remove rows with invalid data

                    // Check if the point falls within the operation boundaries
                    const isInYRange = yValue >= minY && yValue <= maxY;

                    // Return true to keep points that are INSIDE the operation range
                    return isInYRange;
                  });
                });
              }
            });
          }
        }

        return filteredData;
      };

      // Create initial panels when a file is selected
      useEffect(() => {
        if (selectedFile && !initialPanelsCreated) {
          setInitialPanelsCreated(true);
        }
      }, [selectedFile, initialPanelsCreated]);

      // State for filtered data to ensure it updates when filters change
      const [filteredDataState, setFilteredDataState] = useState<any>(null);

      // Update filtered data when filters or selected file change
      useEffect(() => {
        if (selectedFile?.data) {
          console.log('Recalculating filtered data with current filters:', {
            selectedColumns,
            dateFilter,
            conditionalFilters,
            annotationFilters: annotationsState?.annotations?.flatMap((group: any) =>
              group.annotations.filter((annotation: any) => annotation.applyAsFilter)
            ),
            operationFilters: operationsState?.operations?.flatMap((group: any) =>
              group.operations.filter((operation: any) => operation.applyAsFilter)
            )
          });
          const newFilteredData = getFilteredData(selectedFile.data);
          setFilteredDataState(newFilteredData);
        }
      }, [selectedFile?.data, selectedColumns, dateFilter, conditionalFilters, annotationsState, operationsState]);

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const goldenParameter = params.get('golden-parameter');
    if (goldenParameter == 'true') {
      setDataContentSection('golden-parameters')
    }
    else {
      setDataContentSection('file-listing')
    }
  }, [])

  useEffect(() => {
    const currentUrl = window.location.pathname;
    const search = window.location.search;
    const queryParams = new URLSearchParams(search);

    if (
      queryParams.has("golden-parameter") &&
      queryParams.get("golden-parameter") === "true" &&
      queryParams.get("tab") !== "data"
    ) {
      setDataContentSection('file-listing')
      queryParams.delete("golden-parameter");

      const updatedSearch = queryParams.toString();
      const newUrl = `${currentUrl}${updatedSearch ? `?${updatedSearch}` : ""}`;

      window.history.replaceState({}, "", newUrl);
    }
  }, [window.location.pathname, window.location.search]);

  // Handler to save the query for a specific file
  const handleSaveQuery = (query: any, fileName: string) => {
    setQueries((prevQueries) => ({
      ...prevQueries,
      [fileName]: query,
    }));
    setShowQueryBuilder(false);
    console.log(`Query for ${fileName} saved:`, query);
  };

  const onDragOver = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    event.dataTransfer.dropEffect = "move";
    // setReloadSelectSystems(false)
  }, []);

  const onDrop = useCallback((event: React.DragEvent) => {
    event.preventDefault();
    // setReloadSelectSystems(false)
    const data = JSON.parse(
      event.dataTransfer.getData("application/reactflow")
    );
    const dropPosition = {
      x: event.nativeEvent.offsetX,
      y: event.nativeEvent.offsetY,
    };

    const newNode: Node = {
      id: `${data.type}-${Date.now()}`,
      type: "custom",
      position: dropPosition,
      data: {
        label: data?.name,
        id: data?.id,
        type: data?.type,
        file_path: data?.data?.file_path,
        columns: data?.data?.columns,
        isConnected: false,
        onFilterClick: () =>
          handleNodeFilterClick(data.name, data.type, data.columns),
      },
    };

    setNodes((prevNodes) => [...prevNodes, newNode]);
  }, []);

  const handleNodeFilterClick = (
    fileName: string,
    nodeType: string,
    columns: []
  ) => {
    if (nodeType === "file") {
      if (currentFile && currentFile !== fileName && fileQueries[currentFile]) {
        const shouldSave = window.confirm(
          "Do you want to save changes to the current query?"
        );
        if (shouldSave) {
          handleSaveQueryBuilder();
        }
      }
      setCurrentFile(fileName);
      setShowQueryBuilder(true);
      setShowOperationConfig(false);
    } else if (nodeType === "operation") {
      setShowOperationConfig(true);
      setShowQueryBuilder(false);
    }
  };

  const renderTabs = () => {
    const params = new URLSearchParams(location.search);
    const workflowId = params.get('workflowId');
    let showFolderComponent = false;
    if(flowType == 'view'){
      showFolderComponent = viewTabOpen
      console.log('viewTabOpen :', viewTabOpen);
      // setViewTabOpen(false)
    }
    console.log('showFolderComponent :', showFolderComponent);
    if (workflowId === '0') {
      showFolderComponent = true
    }
    console.log('showFolderComponent', showFolderComponent)
    const updateFilelist = (draggedFile: any) => {
      if ((draggedFile as any)?.csv_id) {
        setUploadedFiles((prevFiles: any[]) =>
          prevFiles.map((file: any) =>
            file.csv_id == (draggedFile as any)?.csv_id
              ? { ...file, compatibility: true }
              : file
          )
        );
      }
    }
    return (
      <>
        <div className={(() => {
          const params = new URLSearchParams(location.search);
          const typeParam = params.get('type');
          const explorationTypeParam = params.get('explorationType');
          const isDataExploration = typeParam === 'data-exploration' && (explorationTypeParam === 'batch' || explorationTypeParam === 'plc');

          // Show DataTab sidebar when:
          // 1. On tab1 AND NOT in data exploration mode, OR
          // 2. On tab1 AND there are exploration tabs but no current exploration parameters (back button scenario)
          const hasExplorationTabs = activePanels.length > 0 || selectedFile !== null;
          const showDataTabSidebar = activeTab == 'tab1' && (!isDataExploration || (hasExplorationTabs && !isDataExploration));

          return showDataTabSidebar ? "visible" : "hidden";
        })()}>
          <DataTab
            workbookId={workbookId}
            uploadedFiles={uploadedFiles}
            setUploadedFiles={setUploadedFiles}
            originalChartData={originalChartData}
            setOriginalChartData={setOriginalChartData}
            setFilteredChartData={setFilteredChartData}
            originalFileData={originalFileData}
            setOriginalFileData={setOriginalFileData}
            filteredChartData={filteredChartData}
            setFilteredFileData={setFilteredFileData}
            setShowVisualization={setShowVisualization}
            setActiveTab={setActiveTab}
            setDataContentSection={setDataContentSection}
            setDynamicPlotData={setDynamicPlotData}
            setDynamicPlotLayout={setDynamicPlotLayout}
          />
        </div>
        <div className={activeTab == 'tab2' && showFolderComponent ? "visible" : "hidden"}>
          <FolderStructure setViewTabOpen={setViewTabOpen} />
        </div>
        <div className={activeTab == 'tab2' && !showFolderComponent ? "visible" : "hidden"}>
        {flowType === 'view' ? (
          <div>
            <ViewSidebar
              files={files}
              selectedFile={selectedFile}
              onFileSelect={handleFileSelect}
              onBackClick={resetViewStates}
              activePanels={activePanels}
              isLoading={loadingFiles}
            />
            </ div>
        ) : (
          <div>
            <InsightTab nodes={nodes} updateFilelist={updateFilelist} />
            </div>
          )}

          {/* sidebar  */}
        </div>
        <div className={activeTab == 'tab3' ? "visible" : "hidden"}>
          <AlertTab activeDashboard={activeDashboard} setActiveDashboard={setActiveDashboard} />
        </div>

        {/* Sidebar for data exploration */}
        <div className={(() => {
          const params = new URLSearchParams(location.search);
          const typeParam = params.get('type');
          const explorationTypeParam = params.get('explorationType');
          const isDataExploration = activeTab == 'tab1' && typeParam === 'data-exploration' && (explorationTypeParam === 'batch' || explorationTypeParam === 'plc');
          return isDataExploration ? "visible" : "hidden";
        })()}>
          <ViewSidebar
            files={uploadedFiles}
            selectedFile={selectedFile}
            onFileSelect={handleFileSelect}
            onBackClick={() => {
              // Only change the sidebar by removing exploration parameters from URL
              // Keep the current active tab and ViewContent exactly as they are
              navigate('/?tab=data');
            }}
            activePanels={activePanels}
            isLoading={loadingFiles}
          />
        </div>


      </>

    )
  };

  // Update active tab and workflowId based on URL params
  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const tabParam = params.get('tab');
    const typeParam = params.get('type');
    const explorationTypeParam = params.get('explorationType');
    const workflowIdParam = params.get('workflowId');
    const viewIdParam = params.get('viewId');
    const fileParam = params.get('file');
    const savedFile = localStorage.getItem("selectedFile");

    if (!tabParam) {
      navigate("/?tab=data", { replace: true });
      return; // Exit early since `navigate` will trigger the useEffect again
    }

    // Handle tab switching
    if (tabParam) {
      switch (tabParam) {
        case "insight":
          setActiveTab("tab2");
          if(viewIdParam){
            setFlowType('view')
            break;
          }
          setFlowType('workflow')
          break;
        case "data":
          setActiveTab("tab1");
          if (fileParam && !savedFile) {
            navigate("/?tab=data", { replace: true });
          }

          // Handle data exploration - create tabs based on URL parameters
          if (typeParam === 'data-exploration' && (explorationTypeParam === 'batch' || explorationTypeParam === 'plc')) {
            const tabType = explorationTypeParam as 'batch' | 'plc';
            const tabId = tabType === 'batch' ? 'batch-exploration' : 'plc-exploration';
            const viewName = tabType === 'batch' ? 'Batch Data Exploration' : 'PLC Data Exploration';

            // Add the tab to Redux state (this will make it visible)
            dispatch(addTab({
              fileType: 'ts',
              id: tabId,
              viewName: viewName,
              tabType: tabType
            }));

            console.log(`Created ${tabType} exploration tab from URL parameters`);
          }
          break;
        case "dashboard":
          setActiveTab("tab3");
          break;
      }
    }

    // Set the workflow ID based on the URL param `workflowId`
    if (workflowIdParam) {
      const workflowId = Number(workflowIdParam);
      if (workflowId === 0) {
        setWorkbookId(0);  // Indicate new workflow
      } else {
        setWorkbookId(workflowId);  // Set existing workflow ID
        fetchWorkflowData(workflowId);  // Fetch existing workflow data
      }
    }

    // Legacy support: Fetch view data if viewId is present in the URL
    if (viewIdParam && tabParam === 'insight') {
      const viewId = Number(viewIdParam);
    }
    setShowOperationConfig(false)
    setShowQueryBuilder(false)
  }, [location.search]);  // Re-run effect whenever URL changes

  // Fetch workflow data from the API if the workflowId is greater than 0
  const fetchWorkflowData = async (workflowId: number) => {
    try {
      if (workflowId) {
        // You can update state with the fetched workflow data here
        // For example, setting files or other workflow-related data:
        // setUploadedFiles([]);
        // set([]);

        // Handle other state updates as needed based on the fetched data
      }
    } catch (error) {
      console.error("Error fetching workflow data:", error);
      // Notiflix.Notify.failure("Failed to fetch workflow data");
      message.error("Failed to fetch workflow data");
    }
  };

  // State for view structure
  const [viewStructure, setViewStructure] = useState<any>(null);

  // Helper function to clear exploration state for fresh windows
  const clearExplorationState = () => {
    setSelectedFile(null);
    setFileSelected(false);
    setInitialPanelsCreated(false);
    setActivePanels([]);
    setSelectedColumns({ indices: [], headers: [] });
    setDateFilter({ startDate: null, endDate: null });
    setConditionalFilters([]);
    setPanelFilters({});
    setViewStructure(null);
  };

  // Helper function to map panel type ID to component type
  const getComponentTypeFromPanelTypeId = (panelTypeId: number): ComponentType | null => {
    switch (panelTypeId) {
      case 1:
        return ComponentType.TimeSeriesPanel;
      case 2:
        return ComponentType.OverviewPanel;
      case 3:
        return ComponentType.HistogramPanel;
      case 4:
        return ComponentType.DataTablePanel;
      default:
        return null;
    }
  };

  const handleFileDoubleClick = async (csvId: string) => {
    try {
      const response = await getRequest(`/file/${csvId}`);
      if (response.data.status === 200) {
        const fileData = response.data.data;
        setOriginalFileData(fileData);
        setFilteredFileData(fileData);
        setDataContentSection("data-visualisation");

        // Update URL to include the file ID
        // navigate(`/?tab=data&file=${csvId}`);
      }
    } catch (error) {
      console.error('Error fetching file data:', error);
      // Notiflix.Notify.failure('Failed to fetch file data');
      message.error('Failed to fetch file data');
    }
  };

  const backToFileListing = async () => {
    setDataContentSection("file-listing");
    // Remove file parameter from URL
    navigate('/?tab=data');
  };

  const handleSaveQueryBuilder = (queryData?: any) => {
    if (currentFile && queryData) {
      setNodes(prevNodes =>
        prevNodes.map(node => {
          if (node.data.label === currentFile) {
            return {
              ...node,
              data: {
                ...node.data,
                query: queryData,
                hasQueryConfig: true
              }
            };
          }
          return node;
        })
      );
      console.log('Saving query for file:', currentFile, queryData);
    }
    setShowQueryBuilder(false);
  };

  // Add this function to check if user has access to dashboard
  const hasDashboardAccess = () => {

    const allowedUsers = [
      "coca",
      "chemical",
      "dairy",
      'carbonblack_processengineer',
      'cement'
    ];
    return allowedUsers.includes(authState?.user?.first_name?.toLowerCase() || '');
  };

  // Update the tab click handlers to only include workflowId for insight tab
  const handleTabClick = (tab: string, urlParam: string) => {
    setActiveTab(tab);

    if (urlParam === 'insight') {
      // For insight tab, include workflowId
      const params = new URLSearchParams(location.search);
      const workflowId = params.get('workflowId') || '0';
      const viewId = params.get('viewId');
      if(viewId){
        setFlowType('view')
        return
      }
      setFlowType('workflow')
      navigate(`/?tab=insight&workflowId=${workflowId}`);
    } else {
      // For data and dashboard tabs, only include tab parameter
      navigate(`/?tab=${urlParam}`);

      // Only set dataContentSection to file-listing if we're not in exploration mode
      // Check if there are active exploration tabs or selected files
      const hasExplorationState = activePanels.length > 0 || selectedFile !== null;
      if (!hasExplorationState) {
        setDataContentSection('file-listing');
      }
    }
  };

  const handleTabChange = (tabId: any) => {
    setActiveTab(tabId)

    const params = new URLSearchParams(window.location.search);
    let currentTab = tabId == 'tab1' ? 'data' : tabId == 'tab2' ? 'insight' : tabId == 'tab3' ? 'dashboard' : 'insight';
    params.set("tab", currentTab);
    window.history.pushState({}, "", `?${params.toString()}`);
  }

  return (
    <Flex
      flex={1}
      align="start"
      className={
        collapse
          ? "sidebar-remove relative sidebar-outer"
          : "relative sidebar-outer"
      }
    >
      {/* Tabs Section */}
      <div className="insight-tabs">
        {/* Tab Buttons */}
        <Flex justify="space-between" className="tabs-header">
          <button
            onClick={() => handleTabClick("tab1", "data")}
            style={
              activeTab === "tab1"
                ? {
                  color: "#000",
                  background: "#E9E9F5",
                  borderBottomColor: "#32377F",
                }
                : {}
            }
          >
            <img src={data} alt="data" /> Data
          </button>
          <button
            onClick={() => handleTabClick("tab2", "insight")}
            style={
              activeTab === "tab2"
                ? {
                  color: "#000",
                  background: "#E9E9F5",
                  borderBottomColor: "#32377F",
                }
                : {}
            }
          >
            <img src={insight} alt="insight" /> Insight
          </button>
          {/* Only show dashboard button for authorized users */}
          {hasDashboardAccess() && (
            <button
              onClick={() => handleTabClick("tab3", "dashboard")}
              style={
                activeTab === "tab3"
                  ? {
                    color: "#000",
                    background: "#E9E9F5",
                    borderBottomColor: "#32377F",
                  }
                  : {}
              }
            >
              <img src={box} alt="dashboard" /> Dashboard
            </button>
          )}
        </Flex>

        {/* Tabs Body */}
        <Flex vertical className="tab-content">
          <button
            className={collapse ? "active arrow-btn1" : "arrow-btn1"}
            onClick={() => setCollapse(!collapse)}
          >
            <img src={arrowleft} alt="arrow left" />
          </button>
          <div className="tab-inner">
            {renderTabs()}
          </div>
        </Flex>
      </div>

      {/* Tabs Content */}
      <Flex className="common-box !p-0" flex={1} vertical>
        {/* {activeTab === "tab1" && ( */}
        <div className={`${activeTab === "tab1" ? "block" : "hidden"}`}>
          {/* Check if we're in data exploration mode */}
          {(() => {
            const params = new URLSearchParams(location.search);
            const typeParam = params.get('type');
            const explorationTypeParam = params.get('explorationType');
            const isDataExploration = typeParam === 'data-exploration' && (explorationTypeParam === 'batch' || explorationTypeParam === 'plc');

            // Check if we should show ViewContent (either in exploration mode OR after back button with existing exploration state)
            const hasExplorationState = activePanels.length > 0 || selectedFile !== null;
            const shouldShowViewContent = isDataExploration || hasExplorationState;

            if (shouldShowViewContent) {
              // Render ViewContent for data exploration or when exploration state exists
              return (
                <div className="work-book-box !border-none" style={{ height: '100%' }}>
                  <div className="component-data" style={{ height: 'calc(100% - 56px)' }}>
                    <ViewContent
                      selectedFile={selectedFile}
                      filteredData={filteredDataState || selectedFile?.data}
                      createInitialPanels={fileSelected && !initialPanelsCreated}
                      onPanelsChange={setActivePanels}
                      selectedColumns={selectedColumns}
                      dateFilter={dateFilter}
                      panelFilters={panelFilters}
                      conditionalFilters={conditionalFilters}
                      onColumnSelection={handleColumnSelection}
                      onDateFilterChange={handleDateFilter}
                      onZoomSelection={handleValueRangeFilter}
                      onClearAllFilters={handleClearAllFilters}
                      onAddFilter={handleConditionalFilter}
                      onRemoveFilter={handleRemoveFilter}
                      structure={viewStructure}
                    />
                  </div>
                </div>
              );
            } else {
              // Regular data tab without sidebar
              return (
                <DataTabContent
                  dataContentSection={dataContentSection}
                  setDataContentSection={setDataContentSection}
                  setUploadedFiles={setUploadedFiles}
                  handleFileDoubleClick={handleFileDoubleClick}
                  file={file}
                  uploadedFiles={uploadedFiles}
                  filteredFileData={filteredFileData}
                  setFilteredFileData={setFilteredFileData}
                  dynamicPlotData={dynamicPlotData}
                  setDynamicPlotData={setDynamicPlotData}
                  dynamicPlotLayout={dynamicPlotLayout}
                  setDynamicPlotLayout={setDynamicPlotLayout}
                />
              );
            }
          })()}
        </div>
        {/* {activeTab === "tab2" && ( */}
        <div className={`${activeTab === "tab2" ? "block" : "!hidden"} work-book-box !border-none`} style={{ height: '100%' }}>
          <div className="component-data" style={{ height: 'calc(100% - 56px)' }}>
            {
              flowType=='view' ? (
                <ViewContent
                  selectedFile={selectedFile}
                  filteredData={filteredDataState || selectedFile?.data}
                  createInitialPanels={fileSelected && !initialPanelsCreated}
                  onPanelsChange={setActivePanels}
                  selectedColumns={selectedColumns}
                  dateFilter={dateFilter}
                  panelFilters={panelFilters}
                  conditionalFilters={conditionalFilters}
                  onColumnSelection={handleColumnSelection}
                  onDateFilterChange={handleDateFilter}
                  onZoomSelection={handleValueRangeFilter}
                  onClearAllFilters={handleClearAllFilters}
                  onAddFilter={handleConditionalFilter}
                  onRemoveFilter={handleRemoveFilter}
                  structure={viewStructure}
                />
              ) :(
                <InsightTabContent
                nodes={nodes}
                setNodes={setNodes}
                onDrop={onDrop}
                onDragOver={onDragOver}
                showQueryBuilder={showQueryBuilder}
                setShowQueryBuilder={setShowQueryBuilder}
                onSaveQuery={handleSaveQuery}
                showOperationConfig={showOperationConfig}
                setShowOperationConfig={setShowOperationConfig}
                handleSaveQueryBuilder={handleSaveQueryBuilder}
              // setSelectSystems={setSelectSystems}
              // setReloadSelectSystems={setReloadSelectSystems}
              />
              )
            }

          </div>
        </div>

        <div className={`${activeTab == "tab3" ? "block" : "!hidden"}`}>
          <AlertTabContent activeDashboard={activeDashboard} />
        </div>
        {/* <div className={`${activeTab === "tab4" ? "block" : "!hidden"} work-book-box !border-none`} style={{ height: '100%' }}>
          <div className="component-data" style={{ height: 'calc(100% - 40px)' }}>
            <div>This is what we want </div>
          </div>
        </div> */}

        {/* {activeTab === "tab3" && (
          <>
            <AlertTabContent activeDashboard={activeDashboard}/>
          </>
        )} */}
      </Flex>
      <div className="overlay-text">R64.3.1-v202405071618-SNAPSHOT</div>
    </Flex>
  );
}

export default WorkbookContainer;
