import { Flex, message } from 'antd';
import React, { useEffect, useState } from 'react';
import { Tooltip, Typography, Space, Button } from 'antd';
import { useNavigate } from 'react-router-dom';  // useNavigate instead of useHistory
import plus from '../../img/plus.svg';
import filter from '../../img/filter.svg';
import arrowline from '../../img/arrowline.svg';
import arrowdown from '../../img/arrowblue.svg';
import close from '../../img/cross.svg';
import { getRequest, deleteRequest } from '../../utils/apiHandler';
import { useLocation } from 'react-router-dom';
import ConfirmDeleteModal from '../Modal/ConfirmDeleteModal';
import Notiflix from 'notiflix';

function WorkBookSidebar() {
  const [workFlowId, setWorkFlow] = useState<any>({});
  const [workflowList, setList] = useState<any[]>([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [selectedWorkflow, setSelectedWorkflow] = useState<string>('');
  const [selectedWorkflowId, setSelectedWorkflowId] = useState<number | null>(null);
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    const workflowId = params.get('workflowId');
    if (workflowId) {
      setWorkFlow(workflowId);
    }
    const getAllWorkFlows = async () => {
      const response = await getRequest(`/workflow`);
      if (response.status === 200) {
        const workFlowData = response.data.data;
        setList(workFlowData);
      }
    };
    getAllWorkFlows();
  }, [location]);

  useEffect(() => {
    const params = new URLSearchParams(location.search);
    let workflowId = params.get('workflowId');

     if(location.pathname =='/' && !workflowId)
       navigate('/?tab=insight&workflowId=0')
      
  },[])

  // Function to handle creation of a new workflow
  const handleCreateNewWorkflow = (id: Number) => {
    setWorkFlow(id);
    navigate('/?tab=insight&workflowId=' + id);
  };

  const showDeleteModal = (workflowId: number, workflowName: string) => {
    setSelectedWorkflowId(workflowId);
    setSelectedWorkflow(workflowName);
    setIsModalVisible(true);
  };

  const handleDeleteConfirm = async () => {
    try {
        const response = await deleteRequest(`/workflow/${selectedWorkflowId}`);
        if (response.status === 200) {
          // Notiflix.Notify.success('Workflow deleted successfully');
          message.success('Workflow deleted successfully');
          setIsModalVisible(false);
          setList(prevList => prevList.filter(workflow => workflow.id !== selectedWorkflowId));
          if(workFlowId==selectedWorkflowId){
            navigate('/?tab=insight&workflowId=0');
          }

        }
    } catch (error) {
        console.error('Error deleting workflow:', error);
    }
  };

  const handleDeleteCancel = () => {
    setIsModalVisible(false);
  };

  return (
    <React.Fragment>
      <Flex className="side-tray" vertical>
        <Flex justify="space-between" className="tray-header-btn mb-2">
          <Space>
            <Tooltip title="Arrow Up">
              <Button type="text" className="arrow-btn" onClick={() => handleCreateNewWorkflow(0)}>
                <img src={plus} alt="plus" />
              </Button>
            </Tooltip>
          </Space>
          <Space>
            <Tooltip title="Filter">
              <Button type="text" className="arrow-btn">
                <img src={filter} alt="filter" />
              </Button>
            </Tooltip>
            <Tooltip title="Arrow Line">
              <Button type="text" className="arrow-btn">
                <img src={arrowline} alt="arrowline" />
              </Button>
            </Tooltip>
          </Space>
        </Flex>

        {/* Render the list of workflows */}
        {workflowList?.map((item, index) => (
          <div className='relative group bg-[#e9e9f5] border border-solid border-[#cccccc7d] rounded-md mb-2.5' key={index}>
            <span className={workFlowId == item.id ? 'Active textarea-box' : 'textarea-box'}
              onClick={() => handleCreateNewWorkflow(item.id)}>
              {/* {item.name.charAt(0).toUpperCase()} */}
              {item.id}
            </span>
            <button className='size-5 group-hover:flex hidden items-center justify-center rounded-full absolute -right-2 -top-2 bg-red-500'
              onClick={() => showDeleteModal(item.id, item.name)}>
              <img src={close} alt="close" className='invert brightness-0 relative ' />
            </button>
            <Tooltip title={item.name}>
              <h6 className='m-0 truncate text-sm py-1 px-[5px] border-t border-[#cccccc7d] border-solid bg-[#2529632b]'>
                {item.name}
              </h6>
            </Tooltip>
          </div>
        ))}

        {/* Span that triggers the creation of a new workflow */}
        <span
          className="textarea-box"
          onClick={() => handleCreateNewWorkflow(0)}
          style={{ cursor: 'pointer', backgroundColor: 'white', border: '1px dashed #252963', fontSize: '30px', color: '#252963' }}>
          + {/* Display a "+" symbol for creating new workflow */}
        </span>

        <Flex justify="space-between" align="center">
          <Typography>1</Typography>
          <Tooltip title="arrowdown">
            <Button type="text" className="arrowblue">
              <img src={arrowdown} alt="arrowdown" />
            </Button>
          </Tooltip>
        </Flex>
      </Flex>

      {/* Confirm Delete Modal */}
      <ConfirmDeleteModal
        type="workflow"
        visible={isModalVisible}
        onConfirm={handleDeleteConfirm}
        onCancel={handleDeleteCancel}
        name={selectedWorkflow}
      />
    </React.Fragment>
  );
}

export default WorkBookSidebar;
