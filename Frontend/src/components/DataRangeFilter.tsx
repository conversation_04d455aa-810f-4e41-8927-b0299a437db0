import React, { useState } from 'react';

interface DateRangeFilterProps {
  originalChartData: any;
  originalFileData: any;
  setFilteredChartData: (data: any) => void;
  setFilteredFileData: (data: any) => void;
}

const DateRangeFilter: React.FC<DateRangeFilterProps> = ({
  originalChartData,
  originalFileData,
  setFilteredChartData,
  setFilteredFileData,
}) => {
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');

  const handleFilterData = () => {
    const start = new Date(startDate);
    const end = new Date(endDate);

    const filteredSeries = originalChartData.timeSeries.series.map((serie: any) => {
      const filteredData = serie.data.filter((dataPoint: any) => {
        const date = new Date(dataPoint.x);
        return date >= start && date <= end;
      });
      return { ...serie, data: filteredData };
    });

    // Filter fileData based on the same date range
    const filteredFileData = originalFileData.filter((item: any) => {
      const dateTime = new Date(item.Time);
      return dateTime >= start && dateTime <= end;
    });

    // Update the filtered chart and file data
    setFilteredChartData({
      ...originalChartData,
      timeSeries: { ...originalChartData.timeSeries, series: filteredSeries },
    });

    setFilteredFileData(filteredFileData);
  };

  const handleResetFilters = () => {
    setStartDate('');
    setEndDate('');
    setFilteredChartData(originalChartData); // Reset the chart data to the original
    setFilteredFileData(originalFileData);   // Reset the file data to the original
  };

  return (
    <div className="flex gap-4 mb-4">
      <input
        type="date"
        value={startDate}
        onChange={(e) => setStartDate(e.target.value)}
        className="border rounded-lg p-2"
      />
      <input
        type="date"
        value={endDate}
        onChange={(e) => setEndDate(e.target.value)}
        className="border rounded-lg p-2"
      />
      <button onClick={handleFilterData} className="bg-blue-500 text-white p-2 rounded">
        Filter
      </button>
      <button onClick={handleResetFilters} className="bg-red-500 text-white p-2 rounded">
        Reset
      </button>
    </div>
  );
};

export default DateRangeFilter;
