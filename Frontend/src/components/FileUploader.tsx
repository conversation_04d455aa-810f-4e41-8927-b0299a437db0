import React, { useState } from 'react';
import { parseCSV, parseExcel } from '../utils/fileParsers';
import ParallelTimeSeriesChart from './charts/ParallelTimeSeriesChart';
// import { HistogramWithDataTable } from './charts/HistogramWithDataTable';
import DataTable from './tables/DataTable';
import { Histogram } from './charts/Histogram';

const FileUploadAndRender = () => {
  const [originalChartData, setOriginalChartData] = useState<any>({
    timeSeries: { categories: [], series: [] },
  });
  const [filteredChartData, setFilteredChartData] = useState<any>(originalChartData);
  const [originalFileData, setOriginalFileData] = useState<any>([]);
  const [filteredFileData, setFilteredFileData] = useState<any>(originalFileData);
  const [startDate, setStartDate] = useState('');
  const [endDate, setEndDate] = useState('');

  const handleFileUpload = (e: any) => {
    const file = e.target.files[0];
    const fileType = file.name.split('.').pop();

    if (file) {
      if (fileType === 'csv') {
        parseCSV(file, processData);
      } else if (fileType === 'xlsx' || fileType === 'xls') {
        parseExcel(file, processData);
      }
    }
  };

  const processData = (data: any[]) => {
    if (!data.length) return;

    const allCategories: string[] = [];
    const seriesMap: Record<string, number[]> = {};
    const dataTableData: any[] = [];
    const limitedSeries: any[] = [];
    const limitedCategories: string[] = [];
    const limit = 8000;

    data.sort((a, b) => {
      const dateA = parseDate(a.Time);
      const dateB = parseDate(b.Time);
      return dateA.getTime() - dateB.getTime();
    });

    data.forEach((item: any, indx) => {
      const dateTime = parseDate(item.Time);
      if (isNaN(dateTime.getTime())) return;

      allCategories.push(dateTime.toISOString());
      if (limit >= indx) {
        dataTableData.push(item);
        Object.entries(item).forEach(([key, value]) => {
          if (key !== 'Time') {
            const trimmedKey = key.trim();
            const parsedValue = parseFloat(value as string);

            if (!seriesMap[trimmedKey]) {
              seriesMap[trimmedKey] = [];
              limitedCategories.push(trimmedKey);
            }

            seriesMap[trimmedKey].push(isNaN(parsedValue) ? 0 : parsedValue);
            let singleData = limitedSeries.find((s) => s.name === trimmedKey);

            if (singleData) {
              singleData.data.push({
                x: dateTime,
                y: isNaN(parsedValue) ? 0 : parsedValue,
              });
            } else {
              limitedSeries.push({
                name: trimmedKey,
                data: [
                  {
                    x: dateTime,
                    y: isNaN(parsedValue) ? 0 : parsedValue,
                  },
                ],
              });
            }
          }
        });
      }
    });

    setOriginalFileData(dataTableData); // Store the original file data
    const newChartData = {
      timeSeries: { categories: limitedCategories, series: limitedSeries },
    };
    setOriginalChartData(newChartData); // Store original chart data
    setFilteredChartData(newChartData); // Set filtered data to the new chart data
    setFilteredFileData(dataTableData); // Initially set the filtered data to the original data
  };

  // const processData = (data: any[]) => {
  //   if (!data.length) return;
  
  //   const allCategories: string[] = [];
  //   const seriesMap: Record<string, number[]> = {};
  //   const dataTableData: any[] = [];
  //   const limitedSeries: any[] = [];
  //   const limitedCategories: string[] = [];
  //   const limit = 8000;
  
  //   // Sort data by 'Time' field
  //   data.sort((a, b) => {
  //     const dateA = parseDate(a.Time);
  //     const dateB = parseDate(b.Time);
  //     return dateA.getTime() - dateB.getTime();
  //   });
  
  //   // Find the earliest date
  //   const earliestDate = parseDate(data[0].Time);
  //   const startOfMonth = new Date(earliestDate.getFullYear(), earliestDate.getMonth(), 1);
  //   const endOfMonth = new Date(startOfMonth.getFullYear(), startOfMonth.getMonth() + 1, 0);
  
  //   // Pre-fill start and end date fields with default one-month range
  //   setStartDate(startOfMonth.toISOString().substring(0, 10)); // Format to 'YYYY-MM-DD'
  //   setEndDate(endOfMonth.toISOString().substring(0, 10));
  
  //   // Filter data to include only one month's worth of data
  //   const filteredData = data.filter((item: any) => {
  //     const dateTime = parseDate(item.Time);
  //     return dateTime >= startOfMonth && dateTime <= endOfMonth;
  //   });
  
  //   // Process filtered data as usual
  //   filteredData.forEach((item: any, indx) => {
  //     const dateTime = parseDate(item.Time);
  //     if (isNaN(dateTime.getTime())) return;
  
  //     allCategories.push(dateTime.toISOString());
  //     if (limit >= indx) {
  //       dataTableData.push(item);
  //       Object.entries(item).forEach(([key, value]) => {
  //         if (key !== 'Time') {
  //           const trimmedKey = key.trim();
  //           const parsedValue = parseFloat(value as string);
  
  //           if (!seriesMap[trimmedKey]) {
  //             seriesMap[trimmedKey] = [];
  //             limitedCategories.push(trimmedKey);
  //           }
  
  //           seriesMap[trimmedKey].push(isNaN(parsedValue) ? 0 : parsedValue);
  //           let singleData = limitedSeries.find((s) => s.name === trimmedKey);
  
  //           if (singleData) {
  //             singleData.data.push({
  //               x: dateTime,
  //               y: isNaN(parsedValue) ? 0 : parsedValue,
  //             });
  //           } else {
  //             limitedSeries.push({
  //               name: trimmedKey,
  //               data: [
  //                 {
  //                   x: dateTime,
  //                   y: isNaN(parsedValue) ? 0 : parsedValue,
  //                 },
  //               ],
  //             });
  //           }
  //         }
  //       });
  //     }
  //   });
  
  //   // Set the processed chart and table data
  //   setOriginalFileData(dataTableData);
  //   const newChartData = {
  //     timeSeries: { categories: limitedCategories, series: limitedSeries },
  //   };
  //   setOriginalChartData(newChartData);
  //   setFilteredChartData(newChartData);
  //   setFilteredFileData(dataTableData);
  // };
  

  const parseDate = (dateString: string) => {
    const parts = dateString.split(' ');
    if (parts.length === 2) {
      const dateParts = parts[0].split('/');
      const timeParts = parts[1].split(':');

      if (dateParts.length === 3 && timeParts.length === 2) {
        const day = parseInt(dateParts[0], 10);
        const month = parseInt(dateParts[1], 10) - 1;
        let year = parseInt(dateParts[2], 10);

        if (year < 100) {
          year += year < 50 ? 2000 : 1900;
        }

        const hours = parseInt(timeParts[0], 10);
        const minutes = parseInt(timeParts[1], 10);

        return new Date(year, month, day, hours, minutes);
      }
    }
    return new Date(NaN);
  };

  const handleFilterData = () => {
    const start = new Date(startDate);
    const end = new Date(endDate);

    const filteredSeries = originalChartData.timeSeries.series.map((serie: any) => {
      const filteredData = serie.data.filter((dataPoint: any) => {
        const date = new Date(dataPoint.x);
        const isValidDate = !isNaN(date.getTime());
        const isInRange = date >= start && date <= end;
        return isValidDate && isInRange;
      });
      return { ...serie, data: filteredData };
    });

    // Filter fileData based on the same date range
    const filteredFileData = originalFileData.filter((item: any) => {
      const dateTime = parseDate(item.Time);
      return dateTime >= start && dateTime <= end;
    });

    setFilteredChartData((prevData: any) => ({
      ...prevData,
      timeSeries: {
        ...prevData.timeSeries,
        series: filteredSeries,
      },
    }));

    setFilteredFileData(filteredFileData); // Update the filtered file data
  };

  const handleResetFilters = () => {
    setStartDate('');
    setEndDate('');
    setFilteredChartData(originalChartData);
    setFilteredFileData(originalFileData); // Reset the filtered file data to original data
  };

  return (
    <div className="App">
      <h1>CSV Data Visualization</h1>

      <div style={{ marginBottom: '20px' }}>
        <input
          type="file"
          accept=".csv, .xlsx, .xls"
          onChange={handleFileUpload}
          style={{ marginBottom: '20px' }}
        />
      </div>

      <div className="flex gap-4 mb-4">
        <input
          type="date"
          value={startDate}
          onChange={(e) => setStartDate(e.target.value)}
          className="border rounded-lg p-2"
        />
        <input
          type="date"
          value={endDate}
          onChange={(e) => setEndDate(e.target.value)}
          className="border rounded-lg p-2"
        />
        <button onClick={handleFilterData} className="bg-blue-500 text-white p-2 rounded">
          Filter
        </button>
        <button onClick={handleResetFilters} className="bg-red-500 text-white p-2 rounded">
          Reset
        </button>
      </div>

      {/* <DateRangeFilter
        originalChartData={originalChartData}
        originalFileData={originalFileData}
        setFilteredChartData={setFilteredChartData}
        setFilteredFileData={setFilteredFileData}
      /> */}

      {/* Render the data visualization component when the file is uploaded */}
      {filteredChartData && <ParallelTimeSeriesChart timeSeries={filteredChartData.timeSeries} />}
      {/* {filteredFileData && <HistogramWithDataTable data={filteredFileData} />} */}
      {filteredFileData && <Histogram data={filteredFileData}/>}
      {filteredFileData && <DataTable data={filteredFileData} />}
      {/* <EditableTable/> */}
    </div>
  );
};

export default FileUploadAndRender;
