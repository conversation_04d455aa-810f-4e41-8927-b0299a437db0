import { createSlice, PayloadAction } from '@reduxjs/toolkit';

// === Annotation Types ===
interface AnnotationLine {
  color: string;
  width: number;
  dash: string;
}

interface AnnotationLabel {
  text: string;
  texttemplate: string;
}

interface AnnotationLegendGroupTitle {
  text: string;
  font: {
    weight: string;
    style: string;
    variant: string;
    lineposition: string;
    textcase: string;
    shadow: string;
  };
}

export interface AnnotationShape {
  type: string;
  xref: string;
  yref: string;
  x0: string;
  y0: number;
  x1: string;
  y1: number;
  fillcolor: string;
  line: AnnotationLine;
  editable?: boolean;
  visible?: boolean;
  showlegend?: boolean;
  legend?: string;
  legendgroup?: string;
  legendgrouptitle?: AnnotationLegendGroupTitle;
  legendrank?: number;
  label?: any;
  layer?: string;
  opacity?: number;
  fillrule?: string;
  shapeId?:any;
  selected?: boolean;
  operationType?:string;
  applyAsFilter?: boolean; // New property to indicate if this annotation should be applied as a filter
}

// Grouped by column name
interface AnnotationGroup {
  columnName: string;
  annotations: AnnotationShape[];
}

// === State Type ===
interface AnnotationState {
  annotations: AnnotationGroup[];
  selectedAnnotations: AnnotationGroup[];
  activeColumnName: any;
}

// === Initial State ===
const initialState: AnnotationState = {
  annotations: [],
  selectedAnnotations: [],
  activeColumnName: '',
};

// === Slice ===
const annotationSlice = createSlice({
  name: 'annotations',
  initialState,
  reducers: {
    setAnnotations: (state, action: PayloadAction<AnnotationGroup[]>) => {
      state.annotations = action.payload;
    },
    addAnnotation: (
      state,
      action: PayloadAction<{ columnName: string; annotation: AnnotationShape }>
    ) => {
      const { columnName, annotation } = action.payload;

      // Look for an existing annotations group by columnName
      const group = state.annotations.find(g => g.columnName === columnName);

      if (group) {
        group.annotations.push({ ...annotation, selected: true ,operationType:'anomaly'});
      } else {
        state.annotations.push({
          columnName,
          annotations: [{ ...annotation, selected: true ,operationType:'anomaly'}]
        });
      }



      // const exists = state.selectedAnnotations.some(
      //   (group) => group.columnName === columnName && group.annotations.some(a => JSON.stringify(a) === JSON.stringify(annotation))
      // );

      // // If it doesn't exist, add the annotation to selectedAnnotations for the specific column
      // if (!exists) {
      //   const selectedGroup = state.selectedAnnotations.find(g => g.columnName === columnName);
      //   if (selectedGroup) {
      //     selectedGroup.annotations.push(annotation);
      //   } else {
      //     state.selectedAnnotations.push({ columnName, annotations: [annotation] });
      //   }
      // }
    },


    updateAnnotation: (
      state,
      action: PayloadAction<{
        columnName: string;
        shapeId: string;
        newLabelText: string;
      }>
    ) => {
      const { columnName, shapeId, newLabelText } = action.payload;
      const group = state.annotations.find(g => g.columnName === columnName);

      if (group) {
        const annotation = group.annotations.find(a => a.shapeId === shapeId);
        if (annotation) {
          if (!annotation.label) {
            annotation.label = { text: '' }; // initialize if undefined
          }
          annotation.label.text = newLabelText || '';
        }
      }
      // const selectedGroup = state.selectedAnnotations.find(g => g.columnName === columnName);
      // if (selectedGroup) {
      //   const selectedAnnotation = selectedGroup.annotations.find(a => a.shapeId === shapeId);
      //   if (selectedAnnotation) {
      //     if (!selectedAnnotation.label) {
      //       selectedAnnotation.label = { text: '' };
      //     }
      //     selectedAnnotation.label.text = newLabelText || '';
      //   }
      // }
    },

    removeAnnotation: (
      state,
      action: PayloadAction<{ columnName: string; shapeId: string }>
    ) => {
      const { columnName, shapeId } = action.payload;
      const group = state.annotations.find(g => g.columnName === columnName);
      if (group) {
        // const originalLength = group.annotations.length;
        group.annotations = group.annotations.filter(
          annotation => annotation.shapeId !== shapeId
        );
      }
    },
    

    clearAnnotations: (state) => {
      state.annotations = [];
      state.selectedAnnotations = [];
      state.activeColumnName = [];
    },

    // toggleSelectedAnnotation: (
    //   state,
    //   action: PayloadAction<{ columnName: string; annotation: AnnotationShape }>
    // ) => {
    //   const { columnName, annotation } = action.payload;
    //   const group = state.selectedAnnotations.find(g => g.columnName === columnName);

    //   if (group) {
    //     const index = group.annotations.findIndex(
    //       a => a.shapeId === annotation.shapeId
    //     );

    //     if (index !== -1) {
    //       group.annotations.splice(index, 1);
    //       if (group.annotations.length === 0) {
    //         state.selectedAnnotations = state.selectedAnnotations.filter(
    //           g => g.columnName !== columnName
    //         );
    //       }
    //     } else {
    //       group.annotations.push(annotation);
    //     }
    //   } else {
    //     state.selectedAnnotations.push({ columnName, annotations: [annotation] });
    //   }
    // },
    toggleSelectedAnnotation: (
      state,
      action: PayloadAction<{ columnName: string; shapeId: string }>
    ) => {
      const { columnName, shapeId } = action.payload;
      const group = state.annotations.find(g => g.columnName === columnName);
      if (group) {
        const annotation = group.annotations.find(a => a.shapeId === shapeId);
        if (annotation) {
          annotation.selected = !annotation.selected;
        }
      }
    },
    toggleOperationType: (
      state,
      action: PayloadAction<{ columnName: string; shapeId: string }>
    ) => {
      const { columnName, shapeId } = action.payload;
      const group = state.annotations.find(g => g.columnName === columnName);
      if (group) {
        const annotation = group.annotations.find(a => a.shapeId === shapeId);
        if (annotation) {
          annotation.operationType =
            annotation.operationType === 'operation' ? 'anomaly' : 'operation';
        }
      }
    },

    toggleAnnotationFilter: (
      state,
      action: PayloadAction<{ columnName: string; shapeId: string }>
    ) => {
      const { columnName, shapeId } = action.payload;
      const group = state.annotations.find(g => g.columnName === columnName);
      if (group) {
        const annotation = group.annotations.find(a => a.shapeId === shapeId);
        if (annotation) {
          // Toggle the applyAsFilter property
          annotation.applyAsFilter = !annotation.applyAsFilter;
        }
      }
    },


    // setSelectedAnnotations: (
    //   state,
    //   action: PayloadAction<{ columnName: string; annotation: AnnotationShape }>
    // ) => {
    //   const { columnName, annotation } = action.payload;

    //   // Look for an existing group in selectedAnnotations
    //   const group = state.selectedAnnotations.find(g => g.columnName === columnName);

    //   if (group) {
    //     group.annotations.push(annotation); // ✅ Append to existing group
    //   } else {
    //     state.selectedAnnotations.push({
    //       columnName,
    //       annotations: [annotation] // ✅ Create new group with one annotation
    //     });
    //   }

    // },

    // clearSelectedAnnotations: (state) => {
    //   state.selectedAnnotations = '';
    // },

    setActiveColumnName: (state, action: PayloadAction<string>) => {
      state.activeColumnName = action.payload;
    },


    clearSelectedColumnAnnotations: (state) => {
      state.activeColumnName = '';
    },
  },
});

export const {
  setAnnotations,
  addAnnotation,
  updateAnnotation,
  removeAnnotation,
  clearAnnotations,
  toggleSelectedAnnotation,
  // setSelectedAnnotations,
  // clearSelectedAnnotations,
  toggleOperationType,
  toggleAnnotationFilter,
  setActiveColumnName,
  clearSelectedColumnAnnotations,
} = annotationSlice.actions;

export default annotationSlice.reducer;
