import { createSlice, PayloadAction } from '@reduxjs/toolkit';

interface FileData {
  csv_id: string;
  file_name: string;
  file_path: string;
  version: number;
  created_at?: string;
  updated_at?: string;
  data?: any;
}

interface TabFileData {
  selectedFile: FileData | null;
  filteredData: any;
  selectedColumns: { indices: number[], headers: string[] };
  dateFilter: { startDate: string | null, endDate: string | null };
  panelFilters: Record<string, any[]>;
  conditionalFilters: any[];
  activePanels: string[];
  initialPanelsCreated: boolean;
  lastFetchTime: number;
}

interface FileDataState {
  // Cache for file data by file ID
  fileCache: Record<string, FileData>;
  // Tab-specific data by tab ID
  tabData: Record<string, TabFileData>;
  // Loading states
  loading: Record<string, boolean>;
  // Error states
  errors: Record<string, string | null>;
}

const initialTabData: TabFileData = {
  selectedFile: null,
  filteredData: null,
  selectedColumns: { indices: [], headers: [] },
  dateFilter: { startDate: null, endDate: null },
  panelFilters: {},
  conditionalFilters: [],
  activePanels: [],
  initialPanelsCreated: false,
  lastFetchTime: 0
};

const initialState: FileDataState = {
  fileCache: {},
  tabData: {},
  loading: {},
  errors: {}
};

const fileDataSlice = createSlice({
  name: 'fileData',
  initialState,
  reducers: {
    // Cache file data
    cacheFileData: (state, action: PayloadAction<FileData>) => {
      const file = action.payload;
      state.fileCache[file.csv_id] = {
        ...file,
        data: file.data
      };
    },

    // Set loading state for a file
    setFileLoading: (state, action: PayloadAction<{ fileId: string; loading: boolean }>) => {
      const { fileId, loading } = action.payload;
      state.loading[fileId] = loading;
    },

    // Set error state for a file
    setFileError: (state, action: PayloadAction<{ fileId: string; error: string | null }>) => {
      const { fileId, error } = action.payload;
      state.errors[fileId] = error;
    },

    // Initialize tab data
    initializeTabData: (state, action: PayloadAction<string>) => {
      const tabId = action.payload;
      if (!state.tabData[tabId]) {
        state.tabData[tabId] = { ...initialTabData };
      }
    },

    // Set selected file for a tab
    setTabSelectedFile: (state, action: PayloadAction<{ tabId: string; file: FileData | null }>) => {
      const { tabId, file } = action.payload;
      if (!state.tabData[tabId]) {
        state.tabData[tabId] = { ...initialTabData };
      }
      state.tabData[tabId].selectedFile = file;
      state.tabData[tabId].lastFetchTime = Date.now();
    },

    // Set filtered data for a tab
    setTabFilteredData: (state, action: PayloadAction<{ tabId: string; data: any }>) => {
      const { tabId, data } = action.payload;
      if (!state.tabData[tabId]) {
        state.tabData[tabId] = { ...initialTabData };
      }
      state.tabData[tabId].filteredData = data;
    },

    // Set selected columns for a tab
    setTabSelectedColumns: (state, action: PayloadAction<{ tabId: string; columns: { indices: number[], headers: string[] } }>) => {
      const { tabId, columns } = action.payload;
      if (!state.tabData[tabId]) {
        state.tabData[tabId] = { ...initialTabData };
      }
      state.tabData[tabId].selectedColumns = columns;
    },

    // Set date filter for a tab
    setTabDateFilter: (state, action: PayloadAction<{ tabId: string; filter: { startDate: string | null, endDate: string | null } }>) => {
      const { tabId, filter } = action.payload;
      if (!state.tabData[tabId]) {
        state.tabData[tabId] = { ...initialTabData };
      }
      state.tabData[tabId].dateFilter = filter;
    },

    // Set panel filters for a tab
    setTabPanelFilters: (state, action: PayloadAction<{ tabId: string; filters: Record<string, any[]> }>) => {
      const { tabId, filters } = action.payload;
      if (!state.tabData[tabId]) {
        state.tabData[tabId] = { ...initialTabData };
      }
      state.tabData[tabId].panelFilters = filters;
    },

    // Set conditional filters for a tab
    setTabConditionalFilters: (state, action: PayloadAction<{ tabId: string; filters: any[] }>) => {
      const { tabId, filters } = action.payload;
      if (!state.tabData[tabId]) {
        state.tabData[tabId] = { ...initialTabData };
      }
      state.tabData[tabId].conditionalFilters = filters;
    },

    // Set active panels for a tab
    setTabActivePanels: (state, action: PayloadAction<{ tabId: string; panels: string[] }>) => {
      const { tabId, panels } = action.payload;
      if (!state.tabData[tabId]) {
        state.tabData[tabId] = { ...initialTabData };
      }
      state.tabData[tabId].activePanels = panels;
    },

    // Set initial panels created flag for a tab
    setTabInitialPanelsCreated: (state, action: PayloadAction<{ tabId: string; created: boolean }>) => {
      const { tabId, created } = action.payload;
      if (!state.tabData[tabId]) {
        state.tabData[tabId] = { ...initialTabData };
      }
      state.tabData[tabId].initialPanelsCreated = created;
    },

    // Clear tab data
    clearTabData: (state, action: PayloadAction<string>) => {
      const tabId = action.payload;
      delete state.tabData[tabId];
    },

    // Clear all tab data
    clearAllTabData: (state) => {
      state.tabData = {};
    },

    // Clear file cache
    clearFileCache: (state) => {
      state.fileCache = {};
    }
  }
});

export const {
  cacheFileData,
  setFileLoading,
  setFileError,
  initializeTabData,
  setTabSelectedFile,
  setTabFilteredData,
  setTabSelectedColumns,
  setTabDateFilter,
  setTabPanelFilters,
  setTabConditionalFilters,
  setTabActivePanels,
  setTabInitialPanelsCreated,
  clearTabData,
  clearAllTabData,
  clearFileCache
} = fileDataSlice.actions;

export default fileDataSlice.reducer;
