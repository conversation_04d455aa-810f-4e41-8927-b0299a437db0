# Data Visualization Technical Documentation

## 1. Overview

The Data Visualization module provides a flexible, interactive dashboard for visualizing and analyzing CSV data. It features a drag-and-drop interface with multiple panel types, advanced filtering capabilities, and synchronized data views. This document provides technical details for developers working on this module.

## 2. Component Architecture

### 2.1 Component Hierarchy

```
WorkflowContainer
├── ViewSidebar
└── ViewContent
    ├── AppliedFilters
    ├── GridLayout
    │   └── GridItem
    │       ├── TimeSeriesPanel
    │       ├── OverviewPanel
    │       ├── HistogramPanel
    │       └── DataTablePanel
    └── DateFilterPanel
```

### 2.2 Key Components

#### 2.2.0 WorkflowContainer
- **Purpose**: Parent container that manages global state and coordinates data flow
- **File**: `Frontend/src/components/Dashboard/WorkFlowContainer.tsx`
- **Key Functions**:
  - `handleFileSelect()`: Loads file data and initializes view
  - `handleColumnSelection()`: Updates column selection across all panels
  - `handleDateFilter()`: Updates date filter across all panels
  - `handleValueRangeFilter()`: Handles zoom selection from time series charts
  - `handleConditionalFilter()`: Manages conditional filters from data tables
  - `handleRemoveFilter()`: Removes filters by ID
  - `handleClearAllFilters()`: Clears all active filters
  - `getFilteredData()`: Centralized filtering function that applies all filters

#### 2.2.1 ViewContent
- **Purpose**: Main container for the visualization dashboard
- **File**: `Frontend/src/components/Dashboard/Views Section/ViewContent.tsx`
- **Key Functions**:
  - `handleSaveView()`: Saves the current view configuration to the database
  - Manages panel state and layout
  - Coordinates filter application across panels

#### 2.2.2 GridLayout
- **Purpose**: Manages the drag-and-drop grid layout for panels
- **File**: `Frontend/src/components/Dashboard/Views Section/GridLayout.tsx`
- **Implementation**: Uses `react-grid-layout` for drag-and-drop functionality
- **Key Functions**:
  - `handleContainerDrop()`: Handles dropping components onto the grid
  - `handleLayoutChange()`: Updates layout when panels are moved or resized
  - `handleRemoveItem()`: Removes panels from the grid

#### 2.2.3 Panel Components
- **TimeSeriesPanel**: Displays time series data using Plotly
- **OverviewPanel**: Shows statistical summary of data columns
- **HistogramPanel**: Visualizes data distribution with histograms
- **DataTablePanel**: Presents tabular data with filtering capabilities

## 3. Data Flow

### 3.1 Data Flow Diagram

```
┌─────────────┐     ┌───────────────────┐     ┌───────────────┐
│ CSV File    │────▶│ WorkflowContainer │────▶│ ViewContent   │
└─────────────┘     └─────────┬─────────┘     └───────┬───────┘
                              │                        │
                              ▼                        ▼
┌─────────────┐     ┌───────────────────┐     ┌───────────────┐
│ Redux Store │◀───▶│ Centralized       │────▶│ GridLayout    │
└─────────────┘     │ Filter Management │     └───────┬───────┘
                    └───────────────────┘             │
                                                      ▼
                                              ┌───────────────┐
                                              │ Panel         │
                                              │ Components    │
                                              └───────────────┘
```

### 3.2 Data Processing Flow

1. CSV data is loaded in WorkflowContainer when a file is selected
2. WorkflowContainer maintains the original data and filtered data states
3. WorkflowContainer passes filtered data to ViewContent based on applied filters
4. ViewContent distributes data to GridLayout
5. GridLayout passes data to individual panels
6. Each panel applies any panel-specific visualization logic
7. Filter changes in any panel are sent back to WorkflowContainer
8. WorkflowContainer updates its filter state and recalculates filtered data
9. Updated filtered data flows back down to all panels

## 4. State Management

### 4.1 Component State

#### 4.1.0 WorkflowContainer State
- **File Data State**:
  - `selectedFile`: Currently selected file data
  - `fileSelected`: Boolean tracking if a file is selected
  - `files`: List of available files
  - `loadingFiles`: Loading state for file operations

- **Filter State**:
  - `selectedColumns`: Columns selected for visualization
  - `dateFilter`: Date range filter applied across panels
  - `panelFilters`: Panel-specific filters stored by panel ID
  - `conditionalFilters`: Global conditional filters applied to all panels
  - `initialPanelsCreated`: Tracks if default panels have been created

- **View State**:
  - `activePanels`: Currently active panels in the view
  - `viewStructure`: Layout structure loaded from saved view

#### 4.1.1 ViewContent State
- **Layout State**:
  - `items`: Array of panel items with their types and configurations
  - `layout`: Grid layout configuration for panels
  - `nextId`: Counter for generating unique panel IDs

- **UI State**:
  - `showSaveButton`: Controls visibility of the save button
  - `isSaving`: Loading state during save operations

#### 4.1.2 GridLayout State
- **Grid State**:
  - `layout`: Current layout of grid items
  - `items`: Panel items with their configurations
  - `isDragging`: Tracks if a panel is being dragged

#### 4.1.3 Panel Components State
- **Panel-specific state for each panel type**:
  - TimeSeriesPanel: `activeKey`, `plotData`, `layout`, `showFullData`
  - OverviewPanel: `statistics`, `sortConfig`
  - HistogramPanel: `histogramData`, `selectedColumn`
  - DataTablePanel: `hotRef`, `headers`, `filterConditions`

### 4.2 Redux State

#### 4.2.1 Annotation Slice
- **File**: `Frontend/src/Redux/slices/annotationSlice.ts`
- **Purpose**: Manages annotations on time series charts
- **Key State**:
  - `annotations`: Array of annotation groups by column
  - `activeColumnName`: Currently active column for annotations

#### 4.2.2 Operation Slice
- **File**: `Frontend/src/Redux/slices/operationSlice.ts`
- **Purpose**: Manages operation ranges on time series charts
- **Key State**:
  - `operations`: Array of operation groups by column
  - `selectedOperations`: Currently selected operations
  - `activeColumnName`: Currently active column for operations

#### 4.2.3 Configuration Slice
- **File**: `Frontend/src/Redux/slices/configurationSlice.ts`
- **Purpose**: Manages global configuration data
- **Key State**:
  - `configData`: Configuration options for materials, parameters, etc.
  - `isDataLoaded`: Tracking which data has been loaded
  - `loading`: Loading state for different data types

## 5. Filtering System

### 5.1 Filter Types

- **Column Filters**: Select specific columns to display
- **Date Range Filters**: Filter data by date range
- **Value Range Filters**: Filter numeric data by value range
- **Conditional Column Filters**: Apply conditions to column values
- **Annotation Filters**: Filter data based on annotations
- **Operation Range Filters**: Filter data based on operation ranges

### 5.2 Filter Implementation

#### 5.2.1 Filter Interfaces
- **File**: `Frontend/src/components/Dashboard/Views Section/FilterTypes.ts`
- **Key Interfaces**:
  - `BasePanelFilter`: Base interface for all filters
  - `ColumnFilter`: For column selection
  - `DateRangeFilter`: For date range filtering
  - `ValueRangeFilter`: For numeric value ranges
  - `ConditionalColumnFilter`: For conditional filtering

#### 5.2.2 Filter Application
- **Cross-Panel Synchronization**: Filters applied in one panel affect all panels
- **Filter Persistence**: Filters are saved with view configuration
- **Visual Indicators**: Applied filters are displayed in the UI

### 5.3 Applied Filters Component
- **File**: `Frontend/src/components/Dashboard/Views Section/AppliedFilters.tsx`
- **Purpose**: Displays and manages active filters
- **Key Functions**:
  - `handleRemoveAnnotationFilter()`: Removes annotation filters
  - `handleRemoveOperationFilter()`: Removes operation filters

### 5.4 Filter Synchronization Mechanism

The filter synchronization mechanism ensures that filters applied in one panel affect all panels:

#### 5.4.1 WorkflowContainer as Central Filter Manager

The WorkflowContainer component serves as the central manager for all filters:

```typescript
// In WorkflowContainer.tsx
const [selectedColumns, setSelectedColumns] = useState<ColumnSelection>({ indices: [], headers: [] });
const [dateFilter, setDateFilter] = useState<DateFilter>({ startDate: null, endDate: null });
const [panelFilters, setPanelFilters] = useState<Record<string, PanelFilter[]>>({});
const [conditionalFilters, setConditionalFilters] = useState<PanelFilter[]>([]);

// Centralized filtering function
const getFilteredData = (data: any, zoomData?: any) => {
  if (!data) return null;

  let filteredData = [...data]; // Create a copy of the original data

  // Apply date filter - use zoomData if available, otherwise use dateFilter
  const useZoomData = zoomData && zoomData.x1 && zoomData.x2;
  const startDateStr = useZoomData ? zoomData.x1 : dateFilter.startDate;
  const endDateStr = useZoomData ? zoomData.x2 : dateFilter.endDate;

  // Apply filters in sequence: date filter, column filter, conditional filters
  // ...filter implementation...

  return filteredData;
};
```

#### 5.4.2 Filter Propagation Flow

1. **Filter Creation**: User interacts with a panel, panel calls callback function
2. **Filter State Update**: WorkflowContainer updates its filter state
3. **Filtered Data Recalculation**: WorkflowContainer recalculates filtered data
4. **Propagation to Panels**: Updated data and filter states flow down to panels
5. **Visual Feedback**: AppliedFilters component displays all active filters

## 6. Panel Types

### 6.1 Time Series Panel

- **File**: `Frontend/src/components/Dashboard/Views Section/panels/TimeSeriesPanel.tsx`
- **Library**: Uses Plotly.js for visualization
- **Key Features**:
  - Interactive time series visualization
  - Multiple series display with color differentiation
  - Tab-based navigation between columns
  - Zoom and pan capabilities with y-axis lock
  - Custom toolbar with operation range selection
  - Annotation creation and management
- **State Management**:
  - Uses Redux for annotations and operations
  - Local state for UI controls and visualization settings
- **Key Functions**:
  - `handleZoomSelection()`: Handles zoom events and propagates date filters
  - `handleDrawModeChange()`: Toggles drawing modes for annotations/operations
  - `handleTabChange()`: Switches between column tabs

### 6.2 Overview Panel

- **File**: `Frontend/src/components/Dashboard/Views Section/panels/OverviewPanel.tsx`
- **Key Features**:
  - Statistical summary of selected data
  - Displays column name, mean, min, max, standard deviation
  - Missing values and distinct values counts
  - Dynamic updates when filters are applied
- **Key Functions**:
  - `calculateStatistics()`: Computes statistics for each column
  - `handleColumnSelect()`: Manages column selection for visualization

### 6.3 Histogram Panel

- **File**: `Frontend/src/components/Dashboard/Views Section/panels/HistogramPanel.tsx`
- **Library**: Uses ApexCharts for visualization
- **Key Features**:
  - Distribution visualization for numerical columns
  - Automatic bin calculation and rendering
  - Date-based histograms for temporal analysis
  - Interactive tooltips with bin information
- **Key Functions**:
  - `generateHistograms()`: Creates histogram data for each column
  - `calculateBins()`: Determines optimal bin sizes for histograms

### 6.4 Data Table Panel

- **File**: `Frontend/src/components/Dashboard/Views Section/panels/DataTablePanel.tsx`
- **Library**: Uses Handsontable for interactive tables
- **Key Features**:
  - Interactive data grid with sorting and filtering
  - Column filtering with multiple operators
  - Conditional formatting options
  - Integration with global filter system
- **Key Functions**:
  - `afterFilter()`: Captures filter conditions from Handsontable
  - `applyFilters()`: Applies external filters to the table data

## 7. Save/Load Functionality

### 7.1 View Saving

- **Implementation**: Uses API calls to save view configuration
- **Data Structure**:
  - View metadata (name, folder, file)
  - Panel configurations (type, position, size)
  - Filter settings
  - Layout information

### 7.2 View Loading

- **Implementation**: Loads view configuration from API
- **Process**:
  1. Load view metadata and structure
  2. Create panels based on saved configuration
  3. Apply saved filters
  4. Restore panel positions and sizes

## 8. Implementation Details

### 8.1 Panel Rendering Logic

Each panel type implements its own rendering logic based on the data and filters provided:

#### TimeSeriesPanel Rendering
```typescript
// Process data for the time series chart
const plotData = useMemo(() => {
  // Use filtered data or original data based on toggle state
  const dataToProcess = showFullData ? data : (filteredData || data);

  // Find the datetime column (assumed to be the first column or named "DateTime")
  const dateTimeColumnIndex = dataToProcess[0].findIndex(
    (col: string) => col.toLowerCase() === "datetime"
  );

  // Extract unique column names for tabs
  const columns = dataToProcess[0].filter(
    (col: string, index: number) =>
      index !== dateTimeColumnIndex && typeof col === 'string'
  );

  // Create data series for each column
  return columns.map((column: string, index: number) => {
    // Extract x and y values
    const series = {
      x: dataToProcess.slice(1).map(row => row[dateTimeColumnIndex]),
      y: dataToProcess.slice(1).map(row => row[dataToProcess[0].indexOf(column)]),
      type: 'scatter',
      mode: 'lines',
      name: column
    };
    return { name: column, data: series };
  });
}, [data, filteredData, showFullData]);
```

#### DataTablePanel Filtering
```typescript
// Apply filters to the table data
useEffect(() => {
  if (hotRef.current && conditionalFilters.length > 0) {
    const hot = hotRef.current;
    const filtersPlugin = hot.getPlugin('filters');

    // Clear existing filters
    filtersPlugin.clearConditions();

    // Apply each conditional filter
    conditionalFilters.forEach(filter => {
      if (filter.type === 'conditional-column') {
        const columnFilter = filter as ConditionalColumnFilter;
        const columnIndex = headers.indexOf(columnFilter.column);

        if (columnIndex !== -1) {
          // Map our filter operators to Handsontable operators
          const operator = mapOperator(columnFilter.operator);
          filtersPlugin.addCondition(columnIndex, operator, [columnFilter.value]);
        }
      }
    });

    // Apply the filters
    filtersPlugin.filter();
  }
}, [conditionalFilters, headers]);
```

### 8.2 Cross-Panel Communication

The data visualization system uses a combination of prop drilling and Redux for cross-panel communication:

#### Prop Drilling for Filter Changes
```typescript
// In ViewContent.tsx
const handleZoomSelection = (column: string, min: number, max: number, sourcePanelId?: string) => {
  if (onZoomSelection) {
    onZoomSelection(column, min, max, sourcePanelId);
  }
};

// In parent component
const handleZoomSelection = (column: string, min: number, max: number, sourcePanelId?: string) => {
  // Update date filter state
  setDateFilter({
    startDate: new Date(min).toISOString().split('T')[0],
    endDate: new Date(max).toISOString().split('T')[0]
  });

  // This will propagate to all panels
};
```

#### Redux for Annotations and Operations
```typescript
// In TimeSeriesPanel.tsx
const dispatch = useDispatch();

// Get annotations from Redux store
const annotationsState = useSelector((state: any) => state.annotations);

// Add a new annotation
const handleAddAnnotation = (shape: any) => {
  dispatch(addAnnotation({
    columnName: columnTabs[parseInt(activeKey)].name,
    annotation: {
      shapeId: shape.shapeId,
      x0: shape.x0,
      x1: shape.x1,
      y0: shape.y0,
      y1: shape.y1,
      fillcolor: shape.fillcolor,
      label: shape.label || 'Annotation',
      applyAsFilter: false
    }
  }));
};
```

## 9. Developer Guidelines

### 9.1 Adding a New Panel Type

To add a new panel type:

1. Add a new enum value to `ComponentType` in `types.ts`:
   ```typescript
   export enum ComponentType {
     TimeSeriesPanel = 'TimeSeriesPanel',
     OverviewPanel = 'OverviewPanel',
     HistogramPanel = 'HistogramPanel',
     DataTablePanel = 'DataTablePanel',
     NewPanelType = 'NewPanelType', // Add your new panel type here
   }
   ```

2. Create a new panel component in the `panels` directory:
   ```typescript
   // NewPanel.tsx
   import React from 'react';
   import { ColumnSelection, DateFilter } from '../types';
   import { PanelFilter } from '../FilterTypes';

   interface NewPanelProps {
     data: any;
     filteredData?: any;
     selectedColumns?: ColumnSelection;
     dateFilter?: DateFilter;
     isLoading?: boolean;
     panelFilters?: PanelFilter[];
     conditionalFilters?: PanelFilter[];
     // Add other props as needed
   }

   const NewPanel: React.FC<NewPanelProps> = ({
     data,
     filteredData,
     selectedColumns,
     dateFilter,
     isLoading,
     panelFilters,
     conditionalFilters
   }) => {
     // Implement your panel logic here
     return (
       <div className="new-panel">
         {/* Panel content */}
       </div>
     );
   };

   export default NewPanel;
   ```

3. Update `GridItem.tsx` to render the new panel type:
   ```typescript
   // In the renderPanel function
   case ComponentType.NewPanelType:
     return (
       <NewPanel
         data={fileData}
         filteredData={filteredData}
         selectedColumns={selectedColumns}
         dateFilter={dateFilter}
         isLoading={isLoading}
         panelFilters={panelFilters}
         conditionalFilters={conditionalFilters}
         // Pass other props as needed
       />
     );
   ```

4. Update the panel type ID mapping in `ViewContent.tsx`:
   ```typescript
   const getPanelTypeId = (componentType: ComponentType): number => {
     switch (componentType) {
       case ComponentType.TimeSeriesPanel:
         return 1;
       case ComponentType.OverviewPanel:
         return 2;
       case ComponentType.HistogramPanel:
         return 3;
       case ComponentType.DataTablePanel:
         return 4;
       case ComponentType.NewPanelType:
         return 5; // Assign a new ID
       default:
         return 0;
     }
   };
   ```

### 9.2 Implementing Custom Filters

To add a new filter type:

1. Define the filter interface in `FilterTypes.ts`:
   ```typescript
   export interface CustomFilter extends BasePanelFilter {
     type: 'custom-filter';
     // Add filter-specific properties
     parameter: string;
     threshold: number;
   }

   // Update the union type
   export type PanelFilter = ColumnFilter | DateRangeFilter | ValueRangeFilter |
                            ConditionalColumnFilter | CustomFilter;
   ```

2. Add a helper function to create the filter:
   ```typescript
   export function createCustomFilter(
     id: string,
     parameter: string,
     threshold: number,
     enabled = true
   ): CustomFilter {
     return {
       id,
       type: 'custom-filter',
       enabled,
       parameter,
       threshold
     };
   }
   ```

3. Implement filter application logic in the relevant panel components.

### 9.3 Performance Considerations

1. **Memoization**: Use React's `useMemo` and `useCallback` hooks to prevent unnecessary recalculations:
   ```typescript
   const processedData = useMemo(() => {
     // Expensive data processing
     return data.map(/* ... */);
   }, [data, filters]); // Only recalculate when data or filters change
   ```

2. **Virtualization**: For large datasets, implement virtualization in tables and lists:
   ```typescript
   // In DataTablePanel.tsx
   const tableConfig = {
     // ...other config
     viewportRowRenderingOffset: 20, // Render 20 rows outside viewport
     // ...
   };
   ```

3. **Debouncing**: Debounce filter changes to prevent excessive re-renders:
   ```typescript
   const debouncedFilterChange = useCallback(
     debounce((value) => {
       applyFilter(value);
     }, 300),
     []
   );
   ```

## 10. Future Enhancements

### 10.1 Planned Improvements

1. **Enhanced Filter UI**: Improve the filter interface with more intuitive controls and visual feedback.

2. **Additional Panel Types**:
   - Scatter Plot Panel
   - Box Plot Panel
   - Heatmap Panel

3. **Advanced Annotations**: Add support for more annotation types and improved interaction.

4. **Real-time Data Updates**: Implement WebSocket support for real-time data visualization.

5. **Export Capabilities**: Add options to export visualizations and data in various formats.

### 10.2 Technical Debt

1. **Type Safety**: Improve TypeScript typing throughout the codebase.

2. **Performance Optimization**: Optimize rendering for large datasets.

3. **Code Refactoring**: Extract common functionality into custom hooks and utility functions.

4. **Test Coverage**: Increase unit and integration test coverage for visualization components.
