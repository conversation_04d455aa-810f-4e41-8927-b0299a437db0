version: '3'
services:
  backend:
    build:
      context: .
    ports:
      - "8081:5000"
    environment:
      - NODE_ENV=${NODE_ENV}
      - MONGO_URI=${MONGO_URI}
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
      - DB_HOST=${DB_HOST}
      - DB_PORT=${DB_PORT}
    command: "npm run dev"  # Run npm install before starting the app
    volumes:
      - "./Backend:/app"  # Mount the current directory to /app in the container
      - /app/node_modules  # Prevents mounting node_modules from the host
      - "../OLAM/DATA/:/app/public/datasource"
    working_dir: /app  # Set the working directory inside the container
    depends_on:
      - mongo
      - postgres

  mongo:
    image: mongo:latest
    ports:
      - "27019:27017"  # Expose MongoDB default port
    environment:
      - MONGO_INITDB_DATABASE=${MONGO_INITDB_DATABASE}
    volumes:
      - mongo_data:/data/db

  postgres:
    image: postgres:16
    ports:
      - "5433:5432"
    environment:
      - POSTGRES_DB=${POSTGRES_DB}
      - POSTGRES_USER=${POSTGRES_USER}
      - POSTGRES_PASSWORD=${POSTGRES_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:alpine
    ports:
    - "6379:6379"

volumes:
  mongo_data:
  postgres_data:
